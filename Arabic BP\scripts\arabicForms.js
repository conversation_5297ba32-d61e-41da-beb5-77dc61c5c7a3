/**
 * Arabic Character Forms Mapping
 * Comprehensive mapping of Arabic characters to their contextual forms
 * @version 2.0.0
 */

// Arabic character forms mapping (isolated, initial, medial, final)
export const ARABIC_FORMS = {
    // Basic Arabic Letters
    'ا': { isolated: '\uFE8D', final: '\uFE8E' },
    'ب': { isolated: '\uFE8F', initial: '\uFE91', medial: '\uFE92', final: '\uFE90' },
    'ت': { isolated: '\uFE95', initial: '\uFE97', medial: '\uFE98', final: '\uFE96' },
    'ث': { isolated: '\uFE99', initial: '\uFE9B', medial: '\uFE9C', final: '\uFE9A' },
    'ج': { isolated: '\uFE9D', initial: '\uFE9F', medial: '\uFEA0', final: '\uFE9E' },
    'ح': { isolated: '\uFEA1', initial: '\uFEA3', medial: '\uFEA4', final: '\uFEA2' },
    'خ': { isolated: '\uFEA5', initial: '\uFEA7', medial: '\uFEA8', final: '\uFEA6' },
    'د': { isolated: '\uFEA9', final: '\uFEAA' },
    'ذ': { isolated: '\uFEAB', final: '\uFEAC' },
    'ر': { isolated: '\uFEAD', final: '\uFEAE' },
    'ز': { isolated: '\uFEAF', final: '\uFEB0' },
    'س': { isolated: '\uFEB1', initial: '\uFEB3', medial: '\uFEB4', final: '\uFEB2' },
    'ش': { isolated: '\uFEB5', initial: '\uFEB7', medial: '\uFEB8', final: '\uFEB6' },
    'ص': { isolated: '\uFEB9', initial: '\uFEBB', medial: '\uFEBC', final: '\uFEBA' },
    'ض': { isolated: '\uFEBD', initial: '\uFEBF', medial: '\uFEC0', final: '\uFEBE' },
    'ط': { isolated: '\uFEC1', initial: '\uFEC3', medial: '\uFEC4', final: '\uFEC2' },
    'ظ': { isolated: '\uFEC5', initial: '\uFEC7', medial: '\uFEC8', final: '\uFEC6' },
    'ع': { isolated: '\uFEC9', initial: '\uFECB', medial: '\uFECC', final: '\uFECA' },
    'غ': { isolated: '\uFECD', initial: '\uFECF', medial: '\uFED0', final: '\uFECE' },
    'ف': { isolated: '\uFED1', initial: '\uFED3', medial: '\uFED4', final: '\uFED2' },
    'ق': { isolated: '\uFED5', initial: '\uFED7', medial: '\uFED8', final: '\uFED6' },
    'ك': { isolated: '\uFED9', initial: '\uFEDB', medial: '\uFEDC', final: '\uFEDA' },
    'ل': { isolated: '\uFEDD', initial: '\uFEDF', medial: '\uFEE0', final: '\uFEDE' },
    'م': { isolated: '\uFEE1', initial: '\uFEE3', medial: '\uFEE4', final: '\uFEE2' },
    'ن': { isolated: '\uFEE5', initial: '\uFEE7', medial: '\uFEE8', final: '\uFEE6' },
    'ه': { isolated: '\uFEE9', initial: '\uFEEB', medial: '\uFEEC', final: '\uFEEA' },
    'و': { isolated: '\uFEED', final: '\uFEEE' },
    'ي': { isolated: '\uFEF1', initial: '\uFEF3', medial: '\uFEF4', final: '\uFEF2' },

    // Extended Arabic Letters
    'ء': { isolated: '\uFE80' },
    'آ': { isolated: '\uFE81', final: '\uFE82' },
    'أ': { isolated: '\uFE83', final: '\uFE84' },
    'إ': { isolated: '\uFE87', final: '\uFE88' },
    'ؤ': { isolated: '\uFE85', final: '\uFE86' },
    'ئ': { isolated: '\uFE89', initial: '\uFE8B', medial: '\uFE8C', final: '\uFE8A' },
    'ة': { isolated: '\uFE93', final: '\uFE94' },
    'ى': { isolated: '\uFEEF', final: '\uFEF0' },

    // Lam-Alef Ligatures
    'لا': { isolated: '\uFEFB', final: '\uFEFC' },
    'لأ': { isolated: '\uFEF7', final: '\uFEF8' },
    'لإ': { isolated: '\uFEF9', final: '\uFEFA' },
    'لآ': { isolated: '\uFEF5', final: '\uFEF6' },

    // Persian/Urdu Letters
    'پ': { isolated: '\uFB56', initial: '\uFB58', medial: '\uFB59', final: '\uFB57' },
    'چ': { isolated: '\uFB7A', initial: '\uFB7C', medial: '\uFB7D', final: '\uFB7B' },
    'ژ': { isolated: '\uFB8A', final: '\uFB8B' },
    'گ': { isolated: '\uFB92', initial: '\uFB94', medial: '\uFB95', final: '\uFB93' },
    'ک': { isolated: '\uFB8E', initial: '\uFB90', medial: '\uFB91', final: '\uFB8F' },
    'ی': { isolated: '\uFBFC', initial: '\uFBFE', medial: '\uFBFF', final: '\uFBFD' },

    // Urdu Letters
    'ٹ': { isolated: '\uFB66', initial: '\uFB68', medial: '\uFB69', final: '\uFB67' },
    'ڈ': { isolated: '\uFB88', final: '\uFB89' },
    'ڑ': { isolated: '\uFB8C', final: '\uFB8D' },
    'ں': { isolated: '\uFB9E', final: '\uFB9F' },
    'ھ': { isolated: '\uFBAA', initial: '\uFBAC', medial: '\uFBAD', final: '\uFBAB' },
    'ہ': { isolated: '\uFBA6', initial: '\uFBA8', medial: '\uFBA9', final: '\uFBA7' },
    'ۂ': { isolated: '\uFBA0', final: '\uFBA1' },
    'ۃ': { isolated: '\uFBA4', final: '\uFBA5' },
    'ے': { isolated: '\uFBE8', final: '\uFBE9' },
    'ۓ': { isolated: '\uFBFC', final: '\uFBFD' },

    // Pashto Letters
    'ټ': { isolated: '\uFB67', initial: '\uFB68', medial: '\uFB69', final: '\uFB66' },
    'ځ': { isolated: '\u0681', initial: '\u0681', medial: '\u0681', final: '\u0681' },
    'څ': { isolated: '\u0685', initial: '\u0685', medial: '\u0685', final: '\u0685' },
    'ډ': { isolated: '\u0689', final: '\u0689' },
    'ړ': { isolated: '\u0693', final: '\u0693' },
    'ږ': { isolated: '\u0696', final: '\u0696' },
    'ښ': { isolated: '\u069A', initial: '\u069A', medial: '\u069A', final: '\u069A' },
    'ګ': { isolated: '\u06AB', initial: '\u06AB', medial: '\u06AB', final: '\u06AB' },
    'ڼ': { isolated: '\u06BC', initial: '\u06BC', medial: '\u06BC', final: '\u06BC' },

    // Kurdish Letters
    'ڤ': { isolated: '\uFB6A', initial: '\uFB6C', medial: '\uFB6D', final: '\uFB6B' },
    'ڵ': { isolated: '\uFB8F', initial: '\uFB90', medial: '\uFB91', final: '\uFB8E' },
    'ڕ': { isolated: '\uFB8D', final: '\uFB8E' },
    'ۆ': { isolated: '\uFBD3', final: '\uFBD4' },
    'ۇ': { isolated: '\uFBD5', final: '\uFBD6' },
    'ۈ': { isolated: '\uFBD7', final: '\uFBD8' },
    'ۋ': { isolated: '\uFBD9', final: '\uFBDA' },
    'ۉ': { isolated: '\uFBE1', final: '\uFBE0' },
    'ۊ': { isolated: '\uFBDB', final: '\uFBDC' },
    'ې': { isolated: '\uFBE4', initial: '\uFBE6', medial: '\uFBE7', final: '\uFBE5' },
    'ۅ': { isolated: '\uFBDD', final: '\uFBDE' },
    'ێ': { isolated: '\uFBE8', initial: '\uFBEA', medial: '\uFBEB', final: '\uFBE9' },
    'ۍ': { isolated: '\uFBFC', final: '\uFBFD' },

    // Sindhi Letters
    'ڃ': { isolated: '\uFB9C', initial: '\uFB9E', medial: '\uFB9F', final: '\uFB9D' },
    'ڄ': { isolated: '\uFB94', initial: '\uFB96', medial: '\uFB97', final: '\uFB95' },
    'ڇ': { isolated: '\uFB7C', initial: '\uFB7E', medial: '\uFB7F', final: '\uFB7D' },
    'ڌ': { isolated: '\uFB84', final: '\uFB85' },
    'ڍ': { isolated: '\uFB86', final: '\uFB87' },
    'ڏ': { isolated: '\uFB88', final: '\uFB89' },
    'ڊ': { isolated: '\uFB8A', final: '\uFB8B' },
    'ڙ': { isolated: '\uFB8C', final: '\uFB8D' },
    'ٻ': { isolated: '\uFB90', initial: '\uFB92', medial: '\uFB93', final: '\uFB91' },
    'ٺ': { isolated: '\uFB66', initial: '\uFB68', medial: '\uFB69', final: '\uFB67' },
    'ڻ': { isolated: '\uFB9A', initial: '\uFB9C', medial: '\uFB9D', final: '\uFB9B' },
    'ڨ': { isolated: '\uFB88', initial: '\uFB8A', medial: '\uFB8B', final: '\uFB89' },
    'ڭ': { isolated: '\uFB94', initial: '\uFB96', medial: '\uFB97', final: '\uFB95' },
    'ڜ': { isolated: '\uFB5C', initial: '\uFB5E', medial: '\uFB5F', final: '\uFB5D' },
    'ڥ': { isolated: '\uFB6A', initial: '\uFB6C', medial: '\uFB6D', final: '\uFB6B' },
    'ڦ': { isolated: '\uFB70', initial: '\uFB72', medial: '\uFB73', final: '\uFB71' },

    // Additional Characters
    'ـ': { isolated: '\u0640', initial: '\u0640', medial: '\u0640', final: '\u0640' },
    'ٱ': { isolated: '\u0671', initial: '\u0671', medial: '\u0671', final: '\uFB51' },
    'ٮ': { isolated: '\uFBE4', initial: '\uFBE8', medial: '\uFBE9', final: '\uFBE5' },
    'ڪ': { isolated: '\uFB8E', initial: '\uFB90', medial: '\uFB91', final: '\uFB8F' },

    // Arabic-Indic Digits
    '٠': { isolated: '٠' },
    '١': { isolated: '١' },
    '٢': { isolated: '٢' },
    '٣': { isolated: '٣' },
    '٤': { isolated: '٤' },
    '٥': { isolated: '٥' },
    '٦': { isolated: '٦' },
    '٧': { isolated: '٧' },
    '٨': { isolated: '٨' },
    '٩': { isolated: '٩' },

    // Extended Arabic-Indic Digits
    '۰': { isolated: '۰' },
    '۱': { isolated: '۱' },
    '۲': { isolated: '۲' },
    '۳': { isolated: '۳' },
    '۴': { isolated: '۴' },
    '۵': { isolated: '۵' },
    '۶': { isolated: '۶' },
    '۷': { isolated: '۷' },
    '۸': { isolated: '۸' },
    '۹': { isolated: '۹' },

    // Additional Urdu/Persian Letters
    'ڻ': { isolated: '\uFB9A', initial: '\uFB9C', medial: '\uFB9D', final: '\uFB9B' },
    'ڨ': { isolated: '\uFB88', initial: '\uFB8A', medial: '\uFB8B', final: '\uFB89' },
    'ڭ': { isolated: '\uFB94', initial: '\uFB96', medial: '\uFB97', final: '\uFB95' },
    'ڜ': { isolated: '\uFB5C', initial: '\uFB5E', medial: '\uFB5F', final: '\uFB5D' },
    'ڥ': { isolated: '\uFB6A', initial: '\uFB6C', medial: '\uFB6D', final: '\uFB6B' },
    'ڦ': { isolated: '\uFB70', initial: '\uFB72', medial: '\uFB73', final: '\uFB71' },

    // Kazakh/Kyrgyz Letters
    'ٯ': { isolated: '\u066F', initial: '\u066F', medial: '\u066F', final: '\u066F' },
    'ڢ': { isolated: '\uFB6E', initial: '\uFB70', medial: '\uFB71', final: '\uFB6F' },
    'ڧ': { isolated: '\uFB74', initial: '\uFB76', medial: '\uFB77', final: '\uFB75' },
    'ڡ': { isolated: '\uFBE0', initial: '\uFBE2', medial: '\uFBE3', final: '\uFBE1' },
    'ۏ': { isolated: '\u06CF', final: '\u06CF' },

    // Uyghur Letters
    'ئا': { isolated: '\uFBEA', final: '\uFBEB' },
    'ئە': { isolated: '\uFBEC', final: '\uFBED' },
    'ئو': { isolated: '\uFBEE', final: '\uFBEF' },
    'ئۇ': { isolated: '\uFBF0', final: '\uFBF1' },
    'ئۆ': { isolated: '\uFBF2', final: '\uFBF3' },
    'ئۈ': { isolated: '\uFBF4', final: '\uFBF5' },
    'ئې': { isolated: '\uFBF6', final: '\uFBF7' },
    'ئى': { isolated: '\uFBF8', final: '\uFBF9' },

    // Additional Arabic Supplement Characters
    'ݐ': { isolated: '\u0750', initial: '\u0750', medial: '\u0750', final: '\u0750' },
    'ݑ': { isolated: '\u0751', initial: '\u0751', medial: '\u0751', final: '\u0751' },
    'ݒ': { isolated: '\u0752', initial: '\u0752', medial: '\u0752', final: '\u0752' },
    'ݓ': { isolated: '\u0753', initial: '\u0753', medial: '\u0753', final: '\u0753' },
    'ݔ': { isolated: '\u0754', initial: '\u0754', medial: '\u0754', final: '\u0754' },
    'ݕ': { isolated: '\u0755', initial: '\u0755', medial: '\u0755', final: '\u0755' },
    'ݖ': { isolated: '\u0756', initial: '\u0756', medial: '\u0756', final: '\u0756' },
    'ݗ': { isolated: '\u0757', initial: '\u0757', medial: '\u0757', final: '\u0757' },
    'ݘ': { isolated: '\u0758', initial: '\u0758', medial: '\u0758', final: '\u0758' },
    'ݙ': { isolated: '\u0759', initial: '\u0759', medial: '\u0759', final: '\u0759' },
    'ݚ': { isolated: '\u075A', initial: '\u075A', medial: '\u075A', final: '\u075A' },
    'ݛ': { isolated: '\u075B', initial: '\u075B', medial: '\u075B', final: '\u075B' },
    'ݜ': { isolated: '\u075C', initial: '\u075C', medial: '\u075C', final: '\u075C' },
    'ݝ': { isolated: '\u075D', initial: '\u075D', medial: '\u075D', final: '\u075D' },
    'ݞ': { isolated: '\u075E', initial: '\u075E', medial: '\u075E', final: '\u075E' },
    'ݟ': { isolated: '\u075F', initial: '\u075F', medial: '\u075F', final: '\u075F' },
    'ݠ': { isolated: '\u0760', initial: '\u0760', medial: '\u0760', final: '\u0760' },
    'ݡ': { isolated: '\u0761', initial: '\u0761', medial: '\u0761', final: '\u0761' },
    'ݢ': { isolated: '\u0762', initial: '\u0762', medial: '\u0762', final: '\u0762' },
    'ݣ': { isolated: '\u0763', initial: '\u0763', medial: '\u0763', final: '\u0763' },
    'ݤ': { isolated: '\u0764', initial: '\u0764', medial: '\u0764', final: '\u0764' },
    'ݥ': { isolated: '\u0765', initial: '\u0765', medial: '\u0765', final: '\u0765' },
    'ݦ': { isolated: '\u0766', initial: '\u0766', medial: '\u0766', final: '\u0766' },
    'ݧ': { isolated: '\u0767', initial: '\u0767', medial: '\u0767', final: '\u0767' },
    'ݨ': { isolated: '\u0768', initial: '\u0768', medial: '\u0768', final: '\u0768' },
    'ݩ': { isolated: '\u0769', initial: '\u0769', medial: '\u0769', final: '\u0769' },
    'ݪ': { isolated: '\u076A', initial: '\u076A', medial: '\u076A', final: '\u076A' },
    'ݫ': { isolated: '\u076B', initial: '\u076B', medial: '\u076B', final: '\u076B' },
    'ݬ': { isolated: '\u076C', initial: '\u076C', medial: '\u076C', final: '\u076C' },
    'ݭ': { isolated: '\u076D', initial: '\u076D', medial: '\u076D', final: '\u076D' },
    'ݮ': { isolated: '\u076E', initial: '\u076E', medial: '\u076E', final: '\u076E' },
    'ݯ': { isolated: '\u076F', initial: '\u076F', medial: '\u076F', final: '\u076F' },
    'ݰ': { isolated: '\u0770', initial: '\u0770', medial: '\u0770', final: '\u0770' },
    'ݱ': { isolated: '\u0771', initial: '\u0771', medial: '\u0771', final: '\u0771' },
    'ݲ': { isolated: '\u0772', initial: '\u0772', medial: '\u0772', final: '\u0772' },
    'ݳ': { isolated: '\u0773', initial: '\u0773', medial: '\u0773', final: '\u0773' },
    'ݴ': { isolated: '\u0774', initial: '\u0774', medial: '\u0774', final: '\u0774' },
    'ݵ': { isolated: '\u0775', initial: '\u0775', medial: '\u0775', final: '\u0775' },
    'ݶ': { isolated: '\u0776', initial: '\u0776', medial: '\u0776', final: '\u0776' },
    'ݷ': { isolated: '\u0777', initial: '\u0777', medial: '\u0777', final: '\u0777' },
    'ݸ': { isolated: '\u0778', initial: '\u0778', medial: '\u0778', final: '\u0778' },
    'ݹ': { isolated: '\u0779', initial: '\u0779', medial: '\u0779', final: '\u0779' },
    'ݺ': { isolated: '\u077A', initial: '\u077A', medial: '\u077A', final: '\u077A' },
    'ݻ': { isolated: '\u077B', initial: '\u077B', medial: '\u077B', final: '\u077B' },
    'ݼ': { isolated: '\u077C', initial: '\u077C', medial: '\u077C', final: '\u077C' },
    'ݽ': { isolated: '\u077D', initial: '\u077D', medial: '\u077D', final: '\u077D' },
    'ݾ': { isolated: '\u077E', initial: '\u077E', medial: '\u077E', final: '\u077E' },
    'ݿ': { isolated: '\u077F', initial: '\u077F', medial: '\u077F', final: '\u077F' }
};

/**
 * Get the appropriate form of an Arabic character based on its position
 * @param {string} char - The character to transform
 * @param {string} position - Position type (isolated, initial, medial, final)
 * @returns {string} - The transformed character
 */
export function getCharacterForm(char, position) {
    const forms = ARABIC_FORMS[char];
    
    if (!forms) {
        return char; // Return original character if no forms available
    }
    
    // Try to get the specific form, fallback to available forms
    if (forms[position]) {
        return forms[position];
    }
    
    // Fallback logic
    if (position === 'initial' && !forms.initial) {
        return forms.isolated || char;
    }
    if (position === 'medial' && !forms.medial) {
        return forms.final || forms.isolated || char;
    }
    if (position === 'final' && !forms.final) {
        return forms.isolated || char;
    }
    
    return forms.isolated || char;
}
