/**
 * Enhanced Arabic Language Fix for Minecraft Bedrock 1.21.100+
 * Main entry point with performance optimizations and comprehensive Arabic support
 * @version 2.0.0
 */

import { world, system } from "@minecraft/server";
import {
    processArabicText,
    needsArabicProcessing
} from "./textProcessor.js";
import {
    debounce,
    throttle
} from "./performanceOptimizer.js";
import { getVersion } from "./arabicProcessor.js";
import { processChatText, processSignText as processRTLSignText } from "./rtlSupport.js";
import { throttledEntityProcessing } from "./entityProcessor.js";
import { runAllTests, quickValidationTest } from "./testSuite.js";
import { runQuickTest } from "./quickTest.js";

// Configuration
const CONFIG = {
    SIGN_CHECK_INTERVAL: 20,        // ticks (1 second)
    ITEM_CHECK_INTERVAL: 30,        // ticks (1.5 seconds)
    ENTITY_CHECK_INTERVAL: 40,      // ticks (2 seconds)
    MAX_DISTANCE: 6,                // blocks
    PROCESSED_MARKER: "؀",          // marker for already processed text
    ENABLE_RTL_UI: true,            // enable RTL support for UI elements
    ENABLE_ENTITY_NAMES: true,      // enable entity name processing
    ENABLE_ITEM_FRAMES: true,       // enable item frame text processing
    DEBUG_MODE: false               // enable debug logging
};

// Performance tracking
let totalProcessed = 0;

/**
 * Enhanced chat message processing with RTL support
 */
world.beforeEvents.chatSend.subscribe((event) => {
    const { message, sender } = event;

    // Handle test commands
    if (message.startsWith("!arabic-test")) {
        event.cancel = true;

        if (message === "!arabic-test run") {
            sender.sendMessage("Running Arabic Language Fix tests...");
            const results = runAllTests();
            if (results) {
                sender.sendMessage(`Tests completed: ${results.passed}/${results.total} passed`);
            }
            return;
        }

        if (message.startsWith("!arabic-test validate ")) {
            const testText = message.substring(23); // Remove "!arabic-test validate "
            const validation = quickValidationTest(testText);
            sender.sendMessage(`Validation result: ${JSON.stringify(validation, null, 2)}`);
            return;
        }

        if (message === "!arabic-test quick") {
            sender.sendMessage("Running quick Arabic test...");
            runQuickTest();
            return;
        }

        sender.sendMessage("Arabic test commands: !arabic-test run, !arabic-test validate <text>, !arabic-test quick");
        return;
    }

    if (!needsArabicProcessing(message)) return;

    event.cancel = true;

    try {
        const processedMessage = CONFIG.ENABLE_RTL_UI
            ? processChatText(message)
            : processArabicText(message);

        world.sendMessage(`<${sender.name}> ${processedMessage}`);
        totalProcessed++;

        if (CONFIG.DEBUG_MODE) {
            console.log(`Processed chat message: ${message} -> ${processedMessage}`);
        }
    } catch (error) {
        console.error("Error processing chat message:", error);
        world.sendMessage(`<${sender.name}> ${message}`); // Fallback to original
    }
});

/**
 * Optimized sign text processing with debouncing
 */
const processSignText = debounce("signProcessing", (player) => {
    try {
        const block = player.getBlockFromViewDirection({
            maxDistance: CONFIG.MAX_DISTANCE,
            includeLiquidBlocks: false,
            includePassableBlocks: true
        })?.block;

        if (!block?.typeId?.includes("sign")) return;

        const signComponent = block.getComponent("minecraft:sign");
        if (!signComponent) return;

        const convertSignText = (side) => {
            const text = signComponent.getText(side);
            if (!text || text.startsWith(CONFIG.PROCESSED_MARKER)) return;

            if (needsArabicProcessing(text)) {
                const processedText = CONFIG.PROCESSED_MARKER + processRTLSignText(text);
                signComponent.setText(processedText, side);
                totalProcessed++;

                if (CONFIG.DEBUG_MODE) {
                    console.log(`Processed sign text (${side}): ${text} -> ${processedText}`);
                }
            }
        };

        convertSignText("Back");
        convertSignText("Front");

    } catch (error) {
        console.error("Error processing sign text:", error);
    }
}, 100);

/**
 * Optimized item name processing with throttling
 */
const processItemName = throttle("itemProcessing", (player) => {
    try {
        const inventory = player.getComponent("minecraft:inventory");
        if (!inventory?.container) return;

        const item = inventory.container.getItem(player.selectedSlotIndex);
        if (!item?.nameTag) return;

        if (item.nameTag.startsWith(CONFIG.PROCESSED_MARKER)) return;

        if (needsArabicProcessing(item.nameTag)) {
            const processedName = CONFIG.PROCESSED_MARKER + processArabicText(item.nameTag);
            item.nameTag = processedName;
            inventory.container.setItem(player.selectedSlotIndex, item);
            totalProcessed++;

            if (CONFIG.DEBUG_MODE) {
                console.log(`Processed item name: ${item.nameTag} -> ${processedName}`);
            }
        }

    } catch (error) {
        console.error("Error processing item name:", error);
    }
}, 150);



/**
 * World load event handler - Set up intervals
 */
world.afterEvents.worldLoad.subscribe(() => {
    console.log("Arabic Language Fix Enhanced v2.0.0 - Starting intervals...");

    // Sign text processing interval
    system.runInterval(() => {
        for (const player of world.getPlayers()) {
            processSignText(player);
        }
    }, CONFIG.SIGN_CHECK_INTERVAL);

    // Item name processing interval
    system.runInterval(() => {
        for (const player of world.getPlayers()) {
            processItemName(player);
        }
    }, CONFIG.ITEM_CHECK_INTERVAL);

    // Entity processing interval (includes entity names, item frames, etc.)
    if (CONFIG.ENABLE_ENTITY_NAMES) {
        system.runInterval(() => {
            for (const player of world.getPlayers()) {
                throttledEntityProcessing(player);
            }
        }, CONFIG.ENTITY_CHECK_INTERVAL);
    }

    // Statistics reporting interval (every 5 minutes)
    system.runInterval(() => {
        const { version, supportedLanguages, features } = getVersion();
        console.log(`Arabic Language Fix Enhanced v${version} - Statistics:`);
        console.log(`Total texts processed: ${totalProcessed}`);
        console.log(`Supported languages: ${supportedLanguages.join(", ")}`);
        console.log(`Features: ${features.join(", ")}`);
    }, 6000); // 5 minutes
});

// Initialize
console.log("Arabic Language Fix Enhanced v2.0.0 loaded!");
console.log("Enhanced features: RTL support, performance optimization, comprehensive character set");
console.log("Configuration:", JSON.stringify(CONFIG, null, 2));

// Run quick test on startup
runQuickTest();