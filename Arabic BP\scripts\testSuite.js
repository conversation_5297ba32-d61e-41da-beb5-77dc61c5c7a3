/**
 * Comprehensive Test Suite for Arabic Language Fix
 * Tests all Arabic text processing functionality
 * @version 2.0.0
 */

import { world, system } from "@minecraft/server";
import { processArabicText, needsArabicProcessing, isArabicScript } from './textProcessor.js';
import { processRTLText, detectTextDirection, getTextDirectionInfo } from './rtlSupport.js';
import { getVersion } from './arabicProcessor.js';

// Test configuration
const TEST_CONFIG = {
    ENABLE_TESTS: false,        // Set to true to run tests
    VERBOSE_OUTPUT: true,       // Detailed test output
    AUTO_RUN_INTERVAL: 12000,   // Run tests every 10 minutes
    TEST_TIMEOUT: 5000          // Test timeout in ms
};

// Test cases for Arabic text processing
const TEST_CASES = {
    basic: [
        { input: "مرحبا", expected: "processable", description: "Basic Arabic greeting" },
        { input: "Hello", expected: "not_processable", description: "English text" },
        { input: "مرحبا Hello", expected: "processable", description: "Mixed Arabic-English" },
        { input: "123", expected: "not_processable", description: "Numbers only" },
        { input: "مرحبا 123", expected: "processable", description: "Arabic with numbers" }
    ],
    
    complex: [
        { input: "السلام عليكم ورحمة الله وبركاته", expected: "processable", description: "Long Arabic phrase" },
        { input: "بسم الله الرحمن الرحيم", expected: "processable", description: "Bismillah" },
        { input: "لا إله إلا الله محمد رسول الله", expected: "processable", description: "Shahada" },
        { input: "الحمد لله رب العالمين", expected: "processable", description: "Al-Fatiha excerpt" }
    ],
    
    persian: [
        { input: "سلام", expected: "processable", description: "Persian greeting" },
        { input: "چطوری؟", expected: "processable", description: "Persian question" },
        { input: "خوش آمدید", expected: "processable", description: "Persian welcome" }
    ],
    
    urdu: [
        { input: "آپ کیسے ہیں؟", expected: "processable", description: "Urdu question" },
        { input: "شکریہ", expected: "processable", description: "Urdu thanks" },
        { input: "خوش آمدید", expected: "processable", description: "Urdu welcome" }
    ],
    
    pashto: [
        { input: "ښه راغلاست", expected: "processable", description: "Pashto welcome" },
        { input: "ستاسو نوم څه دی؟", expected: "processable", description: "Pashto name question" },
        { input: "ډېر ښه", expected: "processable", description: "Pashto very good" }
    ],
    
    special: [
        { input: "", expected: "not_processable", description: "Empty string" },
        { input: "   ", expected: "not_processable", description: "Whitespace only" },
        { input: "؀مرحبا", expected: "already_processed", description: "Already processed text" },
        { input: "مرحبا\nأهلا", expected: "processable", description: "Multi-line Arabic" }
    ],
    
    rtl: [
        { input: "مرحبا", direction: "rtl", description: "RTL Arabic text" },
        { input: "Hello مرحبا", direction: "mixed", description: "Mixed direction text" },
        { input: "Hello World", direction: "ltr", description: "LTR English text" }
    ]
};

// Test results storage
let testResults = {
    passed: 0,
    failed: 0,
    total: 0,
    details: []
};

/**
 * Run a single test case
 * @param {Object} testCase - Test case to run
 * @param {string} category - Test category
 * @returns {boolean} - Test passed
 */
function runTestCase(testCase, category) {
    try {
        testResults.total++;
        
        const { input, expected, description } = testCase;
        let result = false;
        let actualOutput = "";
        
        switch (expected) {
            case "processable":
                result = needsArabicProcessing(input);
                if (result) {
                    actualOutput = processArabicText(input);
                }
                break;
                
            case "not_processable":
                result = !needsArabicProcessing(input);
                break;
                
            case "already_processed":
                result = input.startsWith("؀");
                break;
                
            default:
                result = false;
        }
        
        // Special handling for RTL tests
        if (testCase.direction) {
            const detectedDirection = detectTextDirection(input);
            result = detectedDirection === testCase.direction;
            actualOutput = `Direction: ${detectedDirection}`;
        }
        
        const testDetail = {
            category,
            description,
            input,
            expected,
            result,
            actualOutput,
            passed: result
        };
        
        testResults.details.push(testDetail);
        
        if (result) {
            testResults.passed++;
            if (TEST_CONFIG.VERBOSE_OUTPUT) {
                console.log(`✓ [${category}] ${description}: PASSED`);
            }
        } else {
            testResults.failed++;
            console.log(`✗ [${category}] ${description}: FAILED`);
            console.log(`  Input: "${input}"`);
            console.log(`  Expected: ${expected}`);
            console.log(`  Actual: ${actualOutput || "N/A"}`);
        }
        
        return result;
        
    } catch (error) {
        testResults.failed++;
        console.error(`✗ [${category}] ${testCase.description}: ERROR - ${error.message}`);
        return false;
    }
}

/**
 * Run all test cases in a category
 * @param {string} category - Category name
 * @param {Array} tests - Array of test cases
 */
function runTestCategory(category, tests) {
    console.log(`\n--- Running ${category.toUpperCase()} tests ---`);
    
    for (const testCase of tests) {
        runTestCase(testCase, category);
    }
}

/**
 * Run performance tests
 */
function runPerformanceTests() {
    console.log("\n--- Running PERFORMANCE tests ---");
    
    const testText = "السلام عليكم ورحمة الله وبركاته";
    const iterations = 100;
    
    // Test processing speed
    const startTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
        processArabicText(testText);
    }
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / iterations;
    
    console.log(`✓ Performance test: ${iterations} iterations in ${totalTime}ms`);
    console.log(`✓ Average processing time: ${avgTime.toFixed(2)}ms per text`);
    
    // Test cache effectiveness
    const cacheStartTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
        processArabicText(testText); // Should hit cache
    }
    
    const cacheEndTime = Date.now();
    const cacheTime = cacheEndTime - cacheStartTime;
    const cacheAvgTime = cacheTime / iterations;
    
    console.log(`✓ Cache test: ${iterations} cached iterations in ${cacheTime}ms`);
    console.log(`✓ Average cached processing time: ${cacheAvgTime.toFixed(2)}ms per text`);
    
    const speedImprovement = ((avgTime - cacheAvgTime) / avgTime * 100).toFixed(1);
    console.log(`✓ Cache speed improvement: ${speedImprovement}%`);
}

/**
 * Run all tests
 */
function runAllTests() {
    if (!TEST_CONFIG.ENABLE_TESTS) {
        return;
    }
    
    console.log("=== Arabic Language Fix Test Suite ===");
    console.log(`Version: ${getVersion().version}`);
    console.log(`Timestamp: ${new Date().toISOString()}`);
    
    // Reset test results
    testResults = {
        passed: 0,
        failed: 0,
        total: 0,
        details: []
    };
    
    // Run test categories
    runTestCategory("basic", TEST_CASES.basic);
    runTestCategory("complex", TEST_CASES.complex);
    runTestCategory("persian", TEST_CASES.persian);
    runTestCategory("urdu", TEST_CASES.urdu);
    runTestCategory("pashto", TEST_CASES.pashto);
    runTestCategory("special", TEST_CASES.special);
    runTestCategory("rtl", TEST_CASES.rtl);
    
    // Run performance tests
    runPerformanceTests();
    
    // Print summary
    console.log("\n=== TEST SUMMARY ===");
    console.log(`Total tests: ${testResults.total}`);
    console.log(`Passed: ${testResults.passed}`);
    console.log(`Failed: ${testResults.failed}`);
    console.log(`Success rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
    
    if (testResults.failed > 0) {
        console.log("\n=== FAILED TESTS ===");
        testResults.details
            .filter(test => !test.passed)
            .forEach(test => {
                console.log(`- [${test.category}] ${test.description}`);
            });
    }
    
    return testResults;
}

/**
 * Run quick validation test
 * @param {string} text - Text to validate
 * @returns {Object} - Validation result
 */
function quickValidationTest(text) {
    const result = {
        input: text,
        isArabic: isArabicScript(text),
        needsProcessing: needsArabicProcessing(text),
        direction: detectTextDirection(text),
        processed: null,
        rtlInfo: null
    };
    
    if (result.needsProcessing) {
        result.processed = processArabicText(text);
        result.rtlInfo = getTextDirectionInfo(text);
    }
    
    return result;
}

// Auto-run tests if enabled
if (TEST_CONFIG.ENABLE_TESTS && TEST_CONFIG.AUTO_RUN_INTERVAL > 0) {
    system.runInterval(() => {
        runAllTests();
    }, TEST_CONFIG.AUTO_RUN_INTERVAL);
}

export {
    runAllTests,
    runTestCase,
    runTestCategory,
    quickValidationTest,
    testResults,
    TEST_CONFIG,
    TEST_CASES
};
