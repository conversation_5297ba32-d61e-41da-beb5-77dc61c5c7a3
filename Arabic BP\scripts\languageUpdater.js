/**
 * Language File Updater
 * Finds missing entries and adds Arabic translations
 * @version 2.0.0
 */

// Arabic translations for common Minecraft terms
const MINECRAFT_TRANSLATIONS = {
    // UI Elements
    "button": "زر",
    "screen": "شاشة",
    "menu": "قائمة",
    "settings": "إعدادات",
    "options": "خيارات",
    "back": "رجوع",
    "exit": "خروج",
    "close": "إغلاق",
    "cancel": "إلغاء",
    "confirm": "تأكيد",
    "done": "تم",
    "save": "حفظ",
    "load": "تحميل",
    "delete": "حذف",
    "edit": "تحرير",
    "create": "إنشاء",
    "join": "انضمام",
    "leave": "مغادرة",
    "start": "بدء",
    "stop": "توقف",
    "pause": "إيقاف مؤقت",
    "resume": "استئناف",
    "restart": "إعادة تشغيل",
    "reset": "إعادة تعيين",
    "apply": "تطبيق",
    "filter": "تصفية",
    "sort": "ترتيب",
    "search": "بحث",
    "find": "العثور",
    "replace": "استبدال",
    "copy": "نسخ",
    "paste": "لصق",
    "cut": "قص",
    "undo": "تراجع",
    "redo": "إعادة",
    "select": "تحديد",
    "deselect": "إلغاء التحديد",
    "enable": "تمكين",
    "disable": "تعطيل",
    "on": "تشغيل",
    "off": "إيقاف",
    "yes": "نعم",
    "no": "لا",
    "ok": "موافق",
    "error": "خطأ",
    "warning": "تحذير",
    "info": "معلومات",
    "success": "نجح",
    "failed": "فشل",
    "loading": "جاري التحميل",
    "saving": "جاري الحفظ",
    "downloading": "جاري التنزيل",
    "uploading": "جاري الرفع",
    "importing": "جاري الاستيراد",
    "exporting": "جاري التصدير",
    "connecting": "جاري الاتصال",
    "disconnecting": "جاري قطع الاتصال",
    "connected": "متصل",
    "disconnected": "غير متصل",
    "online": "متصل",
    "offline": "غير متصل",
    
    // Game Elements
    "world": "عالم",
    "player": "لاعب",
    "game": "لعبة",
    "server": "خادم",
    "client": "عميل",
    "host": "مضيف",
    "guest": "ضيف",
    "admin": "مدير",
    "moderator": "مشرف",
    "owner": "مالك",
    "member": "عضو",
    "friend": "صديق",
    "team": "فريق",
    "guild": "نقابة",
    "clan": "عشيرة",
    "party": "مجموعة",
    "chat": "دردشة",
    "message": "رسالة",
    "whisper": "همس",
    "broadcast": "بث",
    "announcement": "إعلان",
    "notification": "إشعار",
    "alert": "تنبيه",
    "reminder": "تذكير",
    "tip": "نصيحة",
    "hint": "تلميح",
    "help": "مساعدة",
    "tutorial": "دليل",
    "guide": "مرشد",
    "manual": "دليل",
    "instructions": "تعليمات",
    "rules": "قوانين",
    "terms": "شروط",
    "privacy": "خصوصية",
    "license": "رخصة",
    "credits": "شكر وتقدير",
    "about": "حول",
    "version": "إصدار",
    "update": "تحديث",
    "upgrade": "ترقية",
    "downgrade": "تخفيض",
    "install": "تثبيت",
    "uninstall": "إلغاء التثبيت",
    "download": "تنزيل",
    "upload": "رفع",
    "import": "استيراد",
    "export": "تصدير",
    "backup": "نسخ احتياطي",
    "restore": "استعادة",
    "sync": "مزامنة",
    "refresh": "تحديث",
    "reload": "إعادة تحميل",
    "rebuild": "إعادة بناء",
    "repair": "إصلاح",
    "optimize": "تحسين",
    "clean": "تنظيف",
    "clear": "مسح",
    "empty": "فارغ",
    "full": "ممتلئ",
    "available": "متاح",
    "unavailable": "غير متاح",
    "active": "نشط",
    "inactive": "غير نشط",
    "enabled": "مُمكَّن",
    "disabled": "مُعطَّل",
    "visible": "مرئي",
    "hidden": "مخفي",
    "public": "عام",
    "private": "خاص",
    "secure": "آمن",
    "insecure": "غير آمن",
    "locked": "مقفل",
    "unlocked": "مفتوح",
    "protected": "محمي",
    "unprotected": "غير محمي",
    
    // Accessibility
    "accessibility": "إمكانية الوصول",
    "text to speech": "تحويل النص إلى كلام",
    "speech": "كلام",
    "voice": "صوت",
    "audio": "صوت",
    "sound": "صوت",
    "volume": "مستوى الصوت",
    "mute": "كتم",
    "unmute": "إلغاء الكتم",
    "keyboard": "لوحة المفاتيح",
    "mouse": "فأرة",
    "controller": "وحدة تحكم",
    "gamepad": "لوحة ألعاب",
    "joystick": "عصا التحكم",
    "touchscreen": "شاشة لمس",
    "gesture": "إيماءة",
    "swipe": "تمرير",
    "tap": "نقر",
    "hold": "ضغط مطول",
    "drag": "سحب",
    "drop": "إفلات",
    "scroll": "تمرير",
    "zoom": "تكبير",
    "pan": "تحريك",
    "rotate": "دوران",
    "resize": "تغيير الحجم",
    "move": "نقل",
    "position": "موضع",
    "location": "موقع",
    "coordinates": "إحداثيات",
    "direction": "اتجاه",
    "orientation": "توجه",
    "angle": "زاوية",
    "distance": "مسافة",
    "speed": "سرعة",
    "velocity": "سرعة",
    "acceleration": "تسارع",
    "force": "قوة",
    "power": "قوة",
    "energy": "طاقة",
    "health": "صحة",
    "damage": "ضرر",
    "armor": "درع",
    "weapon": "سلاح",
    "tool": "أداة",
    "item": "عنصر",
    "block": "كتلة",
    "entity": "كائن",
    "mob": "وحش",
    "animal": "حيوان",
    "monster": "وحش",
    "boss": "زعيم",
    "npc": "شخصية غير لاعب",
    "villager": "قروي",
    "trader": "تاجر",
    "merchant": "تاجر",
    "blacksmith": "حداد",
    "farmer": "مزارع",
    "fisherman": "صياد سمك",
    "librarian": "أمين مكتبة",
    "priest": "كاهن",
    "butcher": "جزار",
    "leatherworker": "عامل جلود",
    "fletcher": "صانع أسهم",
    "shepherd": "راعي",
    "toolsmith": "صانع أدوات",
    "weaponsmith": "صانع أسلحة",
    "armorsmith": "صانع دروع",
    "cartographer": "رسام خرائط",
    "cleric": "رجل دين",
    "nitwit": "أحمق",
    "unemployed": "عاطل",
    
    // Common words
    "the": "",
    "and": "و",
    "or": "أو",
    "not": "ليس",
    "is": "هو",
    "are": "هم",
    "was": "كان",
    "were": "كانوا",
    "be": "يكون",
    "been": "كان",
    "have": "لديه",
    "has": "لديه",
    "had": "كان لديه",
    "do": "يفعل",
    "does": "يفعل",
    "did": "فعل",
    "will": "سوف",
    "would": "سوف",
    "could": "يمكن",
    "should": "يجب",
    "may": "قد",
    "might": "قد",
    "can": "يمكن",
    "cannot": "لا يمكن",
    "must": "يجب",
    "shall": "سوف",
    "to": "إلى",
    "from": "من",
    "in": "في",
    "on": "على",
    "at": "في",
    "by": "بواسطة",
    "for": "لـ",
    "with": "مع",
    "without": "بدون",
    "of": "من",
    "about": "حول",
    "above": "فوق",
    "below": "تحت",
    "under": "تحت",
    "over": "فوق",
    "through": "عبر",
    "between": "بين",
    "among": "بين",
    "before": "قبل",
    "after": "بعد",
    "during": "أثناء",
    "while": "بينما",
    "until": "حتى",
    "since": "منذ",
    "ago": "منذ",
    "now": "الآن",
    "then": "ثم",
    "here": "هنا",
    "there": "هناك",
    "where": "أين",
    "when": "متى",
    "why": "لماذا",
    "how": "كيف",
    "what": "ماذا",
    "which": "أي",
    "who": "من",
    "whom": "من",
    "whose": "لمن",
    "this": "هذا",
    "that": "ذلك",
    "these": "هؤلاء",
    "those": "أولئك",
    "all": "جميع",
    "some": "بعض",
    "any": "أي",
    "many": "كثير",
    "much": "كثير",
    "few": "قليل",
    "little": "قليل",
    "more": "أكثر",
    "most": "معظم",
    "less": "أقل",
    "least": "أقل",
    "first": "أول",
    "last": "آخر",
    "next": "التالي",
    "previous": "السابق",
    "new": "جديد",
    "old": "قديم",
    "young": "صغير",
    "big": "كبير",
    "small": "صغير",
    "large": "كبير",
    "huge": "ضخم",
    "tiny": "صغير جداً",
    "long": "طويل",
    "short": "قصير",
    "tall": "طويل",
    "high": "عالي",
    "low": "منخفض",
    "wide": "عريض",
    "narrow": "ضيق",
    "thick": "سميك",
    "thin": "رفيع",
    "heavy": "ثقيل",
    "light": "خفيف",
    "fast": "سريع",
    "slow": "بطيء",
    "quick": "سريع",
    "easy": "سهل",
    "hard": "صعب",
    "difficult": "صعب",
    "simple": "بسيط",
    "complex": "معقد",
    "good": "جيد",
    "bad": "سيء",
    "best": "أفضل",
    "worst": "أسوأ",
    "better": "أفضل",
    "worse": "أسوأ",
    "great": "عظيم",
    "excellent": "ممتاز",
    "perfect": "مثالي",
    "amazing": "مذهل",
    "awesome": "رائع",
    "cool": "رائع",
    "nice": "لطيف",
    "beautiful": "جميل",
    "ugly": "قبيح",
    "pretty": "جميل",
    "cute": "لطيف",
    "hot": "حار",
    "cold": "بارد",
    "warm": "دافئ",
    "cool": "بارد",
    "wet": "مبلل",
    "dry": "جاف",
    "clean": "نظيف",
    "dirty": "متسخ",
    "fresh": "طازج",
    "stale": "قديم",
    "sweet": "حلو",
    "sour": "حامض",
    "bitter": "مر",
    "salty": "مالح",
    "spicy": "حار",
    "mild": "معتدل",
    "strong": "قوي",
    "weak": "ضعيف",
    "soft": "ناعم",
    "rough": "خشن",
    "smooth": "أملس",
    "sharp": "حاد",
    "dull": "كليل",
    "bright": "مشرق",
    "dark": "مظلم",
    "clear": "واضح",
    "cloudy": "غائم",
    "sunny": "مشمس",
    "rainy": "ممطر",
    "snowy": "مثلج",
    "windy": "عاصف",
    "calm": "هادئ",
    "quiet": "هادئ",
    "loud": "عالي",
    "noisy": "صاخب",
    "silent": "صامت"
};

/**
 * Translate English text to Arabic
 * @param {string} englishText - English text to translate
 * @returns {string} - Arabic translation
 */
function translateToArabic(englishText) {
    if (!englishText) return "";

    // Don't translate if it contains variables like %s, %d, etc.
    if (/%[sd%]/.test(englishText)) {
        // For strings with variables, translate around the variables
        return translateWithVariables(englishText);
    }

    // Convert to lowercase for matching
    const lowerText = englishText.toLowerCase();

    // Check for direct translation
    if (MINECRAFT_TRANSLATIONS[lowerText]) {
        return MINECRAFT_TRANSLATIONS[lowerText];
    }

    // Try word-by-word translation
    const words = englishText.split(/\s+/);
    const translatedWords = words.map(word => {
        const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
        return MINECRAFT_TRANSLATIONS[cleanWord] || word;
    });

    return translatedWords.join(' ');
}

/**
 * Translate text with variables
 * @param {string} text - Text with variables like %s, %d
 * @returns {string} - Translated text with preserved variables
 */
function translateWithVariables(text) {
    // Split by variables and translate each part
    const parts = text.split(/(%[sd%])/);
    const translatedParts = parts.map(part => {
        if (/%[sd%]/.test(part)) {
            return part; // Keep variables as-is
        }
        return translateToArabic(part);
    });

    return translatedParts.join('');
}

/**
 * Get common Arabic translations for missing entries
 * @param {string} key - Language key
 * @param {string} englishValue - English value
 * @returns {string} - Arabic translation
 */
function getArabicTranslation(key, englishValue) {
    // Special handling for specific key patterns
    if (key.includes('accessibility')) {
        if (key.includes('tts')) {
            return translateAccessibilityTTS(englishValue);
        }
        return translateAccessibility(englishValue);
    }

    if (key.includes('achievement')) {
        return translateAchievement(englishValue);
    }

    if (key.includes('item.') || key.includes('block.')) {
        return translateItemOrBlock(englishValue);
    }

    if (key.includes('entity.')) {
        return translateEntity(englishValue);
    }

    if (key.includes('biome.')) {
        return translateBiome(englishValue);
    }

    if (key.includes('enchantment.')) {
        return translateEnchantment(englishValue);
    }

    if (key.includes('effect.')) {
        return translateEffect(englishValue);
    }

    // Default translation
    return translateToArabic(englishValue);
}

/**
 * Translate accessibility strings
 * @param {string} text - English accessibility text
 * @returns {string} - Arabic translation
 */
function translateAccessibility(text) {
    const accessibilityMap = {
        "Text To Speech disabled": "تم تعطيل تحويل النص إلى كلام",
        "Text To Speech enabled": "تم تمكين تحويل النص إلى كلام",
        "Press the %s to go back": "اضغط %s للرجوع",
        "Press the %s to exit chat": "اضغط %s للخروج من الدردشة",
        "Press the %s to send message": "اضغط %s لإرسال الرسالة",
        "%s says %s": "%s يقول %s",
        "Hide chat": "إخفاء الدردشة",
        "Keyboard": "لوحة المفاتيح",
        "Mute All": "كتم الجميع",
        "Send": "إرسال",
        "Chat": "دردشة",
        "Player view": "عرض اللاعب",
        "Done": "تم",
        "Download begun": "بدأ التنزيل",
        "Downloading %s percent": "جاري التنزيل %s بالمائة",
        "Download canceled": "تم إلغاء التنزيل",
        "Download complete": "اكتمل التنزيل",
        "Import begun": "بدأ الاستيراد",
        "Importing %s percent": "جاري الاستيراد %s بالمائة",
        "Import canceled": "تم إلغاء الاستيراد",
        "Import complete": "اكتمل الاستيراد",
        "Signing into X box live": "جاري تسجيل الدخول إلى Xbox Live",
        "Press %s to open chat": "اضغط %s لفتح الدردشة",
        "Press or Hold %s to Emote": "اضغط أو اضغط مطولاً %s للتعبير",
        "Mouse Button %s": "زر الفأرة %s",
        "Controller %s": "وحدة التحكم %s",
        "%s or %s": "%s أو %s",
        ", %s": "، %s",
        ", or %s": "، أو %s",
        "Button": "زر",
        "Checkbox": "مربع اختيار",
        "status %s": "الحالة %s",
        "Dropdown": "قائمة منسدلة",
        "Image": "صورة",
        "%s Screen": "شاشة %s",
        "Slider": "شريط تمرير",
        "Tab": "تبويب",
        "Textbox": "مربع نص",
        "Toggle": "تبديل",
        "Scroll Bar": "شريط التمرير",
        "Left Arrow": "سهم يسار",
        "Right Arrow": "سهم يمين",
        "close": "إغلاق",
        "back": "رجوع",
        "Exit": "خروج",
        "Filter": "تصفية",
        " %s Applied": " %s مطبق",
        "Sort": "ترتيب"
    };

    return accessibilityMap[text] || translateToArabic(text);
}

/**
 * Translate accessibility TTS strings
 * @param {string} text - English TTS text
 * @returns {string} - Arabic translation
 */
function translateAccessibilityTTS(text) {
    // TTS strings often need special handling
    return translateAccessibility(text);
}

/**
 * Translate achievement strings
 * @param {string} text - English achievement text
 * @returns {string} - Arabic translation
 */
function translateAchievement(text) {
    // Achievement translations are already in the file
    return translateToArabic(text);
}

/**
 * Translate item or block names
 * @param {string} text - English item/block name
 * @returns {string} - Arabic translation
 */
function translateItemOrBlock(text) {
    const itemMap = {
        "Stone": "حجر",
        "Dirt": "تراب",
        "Grass": "عشب",
        "Wood": "خشب",
        "Iron": "حديد",
        "Gold": "ذهب",
        "Diamond": "ماس",
        "Coal": "فحم",
        "Water": "ماء",
        "Lava": "حمم",
        "Sand": "رمل",
        "Gravel": "حصى",
        "Glass": "زجاج",
        "Wool": "صوف",
        "Brick": "طوب",
        "Obsidian": "سبج",
        "Bedrock": "صخر أساسي"
    };

    return itemMap[text] || translateToArabic(text);
}

/**
 * Translate entity names
 * @param {string} text - English entity name
 * @returns {string} - Arabic translation
 */
function translateEntity(text) {
    const entityMap = {
        "Zombie": "زومبي",
        "Skeleton": "هيكل عظمي",
        "Creeper": "كريبر",
        "Spider": "عنكبوت",
        "Enderman": "إندرمان",
        "Pig": "خنزير",
        "Cow": "بقرة",
        "Chicken": "دجاجة",
        "Sheep": "خروف",
        "Horse": "حصان",
        "Wolf": "ذئب",
        "Cat": "قطة",
        "Villager": "قروي"
    };

    return entityMap[text] || translateToArabic(text);
}

/**
 * Translate biome names
 * @param {string} text - English biome name
 * @returns {string} - Arabic translation
 */
function translateBiome(text) {
    const biomeMap = {
        "Desert": "صحراء",
        "Forest": "غابة",
        "Ocean": "محيط",
        "Plains": "سهول",
        "Mountains": "جبال",
        "Swamp": "مستنقع",
        "Jungle": "أدغال",
        "Tundra": "تندرا",
        "Taiga": "تايغا"
    };

    return biomeMap[text] || translateToArabic(text);
}

/**
 * Translate enchantment names
 * @param {string} text - English enchantment name
 * @returns {string} - Arabic translation
 */
function translateEnchantment(text) {
    const enchantmentMap = {
        "Sharpness": "حدة",
        "Protection": "حماية",
        "Efficiency": "كفاءة",
        "Unbreaking": "عدم الكسر",
        "Fortune": "حظ",
        "Silk Touch": "لمسة حريرية",
        "Fire Aspect": "جانب النار",
        "Knockback": "دفع للخلف",
        "Looting": "نهب",
        "Respiration": "تنفس",
        "Aqua Affinity": "تقارب مائي",
        "Thorns": "أشواك",
        "Depth Strider": "خطوات العمق",
        "Frost Walker": "مشي الصقيع",
        "Mending": "إصلاح",
        "Curse of Binding": "لعنة الربط",
        "Curse of Vanishing": "لعنة الاختفاء"
    };

    return enchantmentMap[text] || translateToArabic(text);
}

/**
 * Translate effect names
 * @param {string} text - English effect name
 * @returns {string} - Arabic translation
 */
function translateEffect(text) {
    const effectMap = {
        "Speed": "سرعة",
        "Slowness": "بطء",
        "Haste": "عجلة",
        "Mining Fatigue": "إرهاق التعدين",
        "Strength": "قوة",
        "Instant Health": "صحة فورية",
        "Instant Damage": "ضرر فوري",
        "Jump Boost": "تعزيز القفز",
        "Nausea": "غثيان",
        "Regeneration": "تجديد",
        "Resistance": "مقاومة",
        "Fire Resistance": "مقاومة النار",
        "Water Breathing": "تنفس تحت الماء",
        "Invisibility": "اختفاء",
        "Blindness": "عمى",
        "Night Vision": "رؤية ليلية",
        "Hunger": "جوع",
        "Weakness": "ضعف",
        "Poison": "سم",
        "Wither": "ذبول",
        "Health Boost": "تعزيز الصحة",
        "Absorption": "امتصاص",
        "Saturation": "إشباع",
        "Glowing": "توهج",
        "Levitation": "رفع",
        "Luck": "حظ",
        "Bad Luck": "سوء حظ"
    };

    return effectMap[text] || translateToArabic(text);
}

export {
    MINECRAFT_TRANSLATIONS,
    translateToArabic,
    getArabicTranslation,
    translateWithVariables
};
