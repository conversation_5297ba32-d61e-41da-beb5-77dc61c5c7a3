/**
 * Enhanced Arabic Text Processing Engine
 * High-performance text processing with caching and RTL support
 * @version 2.0.0
 */

import { 
    isArabicScript, 
    stripDiacritics, 
    processLamAlefLigatures, 
    determinePosition,
    textCache,
    CACHE_SIZE_LIMIT
} from './arabicProcessor.js';
import { getCharacterForm } from './arabicForms.js';

// Performance tracking
let processedCount = 0;
let cacheHits = 0;

/**
 * Clean cache when it gets too large
 */
function cleanCache() {
    if (textCache.size > CACHE_SIZE_LIMIT) {
        const keysToDelete = Array.from(textCache.keys()).slice(0, Math.floor(CACHE_SIZE_LIMIT / 2));
        keysToDelete.forEach(key => textCache.delete(key));
    }
}

/**
 * Process a chunk of Arabic text
 * @param {string} text - Text chunk to process
 * @returns {string} - Processed text
 */
function processChunk(text) {
    let result = '';
    
    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        
        if (isArabicScript(char)) {
            const position = determinePosition(text, i);
            result += getCharacterForm(char, position);
        } else {
            result += char;
        }
    }
    
    return result;
}

/**
 * Reverse text for RTL display
 * @param {string} text - Text to reverse
 * @returns {string} - Reversed text
 */
function reverseText(text) {
    return text.split('').reverse().join('');
}

/**
 * Process mixed Arabic and non-Arabic text
 * @param {string} text - Mixed text
 * @returns {string} - Processed text
 */
function processMixedText(text) {
    const words = text.split(/(\s+)/);
    const processedWords = [];
    
    for (const word of words) {
        if (isArabicScript(word)) {
            processedWords.push(processArabicText(word, false));
        } else {
            processedWords.push(word);
        }
    }
    
    return processedWords.join('');
}

/**
 * Main Arabic text processing function
 * @param {string} text - Text to process
 * @param {boolean} reverseOrder - Whether to reverse the text order (default: true)
 * @param {boolean} useCache - Whether to use caching (default: true)
 * @returns {string} - Processed Arabic text
 */
function processArabicText(text, reverseOrder = true, useCache = true) {
    if (!text || typeof text !== 'string') {
        return text || '';
    }
    
    // Check cache first
    const cacheKey = `${text}:${reverseOrder}`;
    if (useCache && textCache.has(cacheKey)) {
        cacheHits++;
        return textCache.get(cacheKey);
    }
    
    try {
        processedCount++;
        
        // Skip processing if no Arabic characters
        if (!isArabicScript(text)) {
            return text;
        }
        
        // Clean and prepare text
        let processedText = stripDiacritics(text);
        processedText = processLamAlefLigatures(processedText);
        
        // Process in chunks for better performance
        const chunkSize = 500; // Reduced chunk size for better responsiveness
        let result = '';
        
        if (processedText.length > chunkSize) {
            for (let i = 0; i < processedText.length; i += chunkSize) {
                const chunk = processedText.substring(i, i + chunkSize);
                result += processChunk(chunk);
            }
        } else {
            result = processChunk(processedText);
        }
        
        // Apply RTL reversal if requested
        if (reverseOrder) {
            result = reverseText(result);
        }
        
        // Cache the result
        if (useCache) {
            cleanCache();
            textCache.set(cacheKey, result);
        }
        
        return result;
        
    } catch (error) {
        console.error("Error processing Arabic text:", error);
        return text; // Return original text on error
    }
}

/**
 * Process text with RTL support for UI elements
 * @param {string} text - Text to process
 * @returns {string} - Processed text with RTL markers
 */
function processTextWithRTL(text) {
    if (!isArabicScript(text)) {
        return text;
    }
    
    const processed = processArabicText(text, true, true);
    // Add RTL marker for better UI support
    return `\u202E${processed}\u202C`;
}

/**
 * Batch process multiple texts for efficiency
 * @param {string[]} texts - Array of texts to process
 * @param {boolean} reverseOrder - Whether to reverse text order
 * @returns {string[]} - Array of processed texts
 */
function batchProcessTexts(texts, reverseOrder = true) {
    return texts.map(text => processArabicText(text, reverseOrder, true));
}

/**
 * Get processing statistics
 * @returns {Object} - Processing statistics
 */
function getProcessingStats() {
    return {
        processedCount,
        cacheHits,
        cacheSize: textCache.size,
        hitRate: processedCount > 0 ? (cacheHits / processedCount * 100).toFixed(2) + '%' : '0%'
    };
}

/**
 * Clear processing cache and reset stats
 */
function clearCache() {
    textCache.clear();
    processedCount = 0;
    cacheHits = 0;
}

/**
 * Check if text needs Arabic processing
 * @param {string} text - Text to check
 * @returns {boolean} - True if needs processing
 */
function needsArabicProcessing(text) {
    return text && typeof text === 'string' && isArabicScript(text);
}

export {
    processArabicText,
    processTextWithRTL,
    processMixedText,
    batchProcessTexts,
    needsArabicProcessing,
    getProcessingStats,
    clearCache,
    isArabicScript
};
