/**
 * Enhanced Arabic Text Processor for Minecraft Bedrock 1.21.100+
 * Optimized for performance with caching and comprehensive Arabic script support
 * @version 2.0.0
 * <AUTHOR> Bedrock Arabic Enhancement
 */

// Performance optimization: Cache for processed text
const textCache = new Map();
const CACHE_SIZE_LIMIT = 1000;

// Arabic script character ranges (comprehensive)
const ARABIC_RANGES = [
    [0x0600, 0x06FF], // Arabic
    [0x0750, 0x077F], // Arabic Supplement
    [0x08A0, 0x08FF], // Arabic Extended-A
    [0xFB50, 0xFDFF], // Arabic Presentation Forms-A
    [0xFE70, 0xFEFF], // Arabic Presentation Forms-B
    [0x1EE00, 0x1EEFF] // Arabic Mathematical Alphabetic Symbols
];

// Diacritics (Harakat) for removal
const DIACRITICS_REGEX = /[\u064B-\u065F\u0670\u06D6-\u06ED\u08D4-\u08E1\u08E3-\u08FF]/g;

// Characters that don't connect to the next character
const NON_CONNECTING_CHARS = new Set([
    'ا', 'د', 'ذ', 'ر', 'ز', 'و', 'آ', 'أ', 'إ', 'ؤ',
    'ۀ', 'ژ', 'ڑ', 'ڈ', 'ډ', 'ړ', 'ږ', 'ڗ', 'ڙ', 
    'ۆ', 'ۇ', 'ۈ', 'ۋ', 'ۅ', 'ۉ', 'ې', 'ے'
]);

// Punctuation characters
const PUNCTUATION_REGEX = /[!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~،؛؟]/;

/**
 * Check if text contains Arabic script characters
 * @param {string} text - Text to check
 * @returns {boolean} - True if contains Arabic characters
 */
function isArabicScript(text) {
    if (!text || typeof text !== 'string') return false;
    
    for (const char of text) {
        const code = char.codePointAt(0);
        for (const [start, end] of ARABIC_RANGES) {
            if (code >= start && code <= end) {
                return true;
            }
        }
    }
    return false;
}

/**
 * Check if character is punctuation
 * @param {string} char - Character to check
 * @returns {boolean} - True if punctuation
 */
function isPunctuation(char) {
    return PUNCTUATION_REGEX.test(char);
}

/**
 * Remove diacritics from Arabic text
 * @param {string} text - Text to process
 * @returns {string} - Text without diacritics
 */
function stripDiacritics(text) {
    return text.replace(DIACRITICS_REGEX, '');
}

/**
 * Process Lam-Alef ligatures
 * @param {string} text - Text to process
 * @returns {string} - Text with ligatures
 */
function processLamAlefLigatures(text) {
    return text
        .replace(/لا/g, '\uFEFB')
        .replace(/لأ/g, '\uFEF7')
        .replace(/لإ/g, '\uFEF9')
        .replace(/لآ/g, '\uFEF5');
}

/**
 * Determine character position in word (isolated, initial, medial, final)
 * @param {string} text - Full text
 * @param {number} index - Character index
 * @returns {string} - Position type
 */
function determinePosition(text, index) {
    const char = text[index];
    const prevChar = index > 0 ? text[index - 1] : null;
    const nextChar = index < text.length - 1 ? text[index + 1] : null;

    const connectsToPrev = prevChar && canConnectToPrev(char, prevChar);
    const connectsToNext = nextChar && canConnectToNext(char, nextChar);

    if (!connectsToPrev && !connectsToNext) {
        return 'isolated';
    } else if (!connectsToPrev && connectsToNext) {
        return 'initial';
    } else if (connectsToPrev && !connectsToNext) {
        return 'final';
    } else {
        return 'medial';
    }
}

/**
 * Check if character can connect to previous character
 * @param {string} char - Current character
 * @param {string} prevChar - Previous character
 * @returns {boolean} - True if can connect
 */
function canConnectToPrev(char, prevChar) {
    if (!prevChar || isPunctuation(prevChar) || !isArabicScript(prevChar)) {
        return false;
    }
    
    if (char === 'ـ') {
        return !NON_CONNECTING_CHARS.has(prevChar);
    }
    return !NON_CONNECTING_CHARS.has(prevChar);
}

/**
 * Check if character can connect to next character
 * @param {string} char - Current character
 * @param {string} nextChar - Next character
 * @returns {boolean} - True if can connect
 */
function canConnectToNext(char, nextChar) {
    if (!nextChar || isPunctuation(char) || isPunctuation(nextChar) || !isArabicScript(nextChar)) {
        return false;
    }
    
    if (char === 'ـ') {
        return true;
    }
    return !NON_CONNECTING_CHARS.has(char);
}

/**
 * Get version information
 * @returns {Object} - Version and supported languages
 */
function getVersion() {
    return {
        version: "2.0.0",
        supportedLanguages: [
            "Arabic", "Persian", "Urdu", "Pashto", "Kurdish", 
            "Sindhi", "Uyghur", "Kazakh", "Kyrgyz", "Tajik"
        ],
        features: [
            "RTL Support", "Performance Optimized", "Comprehensive Character Set",
            "Ligature Processing", "Contextual Forms", "Caching System"
        ]
    };
}

export {
    isArabicScript,
    stripDiacritics,
    processLamAlefLigatures,
    determinePosition,
    canConnectToPrev,
    canConnectToNext,
    isPunctuation,
    getVersion,
    textCache,
    CACHE_SIZE_LIMIT,
    NON_CONNECTING_CHARS
};
