/**
 * Find Missing Language Entries
 * Compares English and Arabic language files to find missing translations
 * @version 2.0.0
 */

import { getArabicTranslation } from './languageUpdater.js';

/**
 * Parse language file content into key-value pairs
 * @param {string} content - Language file content
 * @returns {Map} - Map of key-value pairs
 */
function parseLanguageFile(content) {
    const entries = new Map();
    const lines = content.split('\n');
    
    for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine || trimmedLine.startsWith('#') || trimmedLine.startsWith('//')) {
            continue; // Skip comments and empty lines
        }
        
        const equalIndex = trimmedLine.indexOf('=');
        if (equalIndex === -1) {
            continue; // Skip invalid lines
        }
        
        const key = trimmedLine.substring(0, equalIndex).trim();
        const value = trimmedLine.substring(equalIndex + 1).trim();
        
        if (key && value) {
            entries.set(key, value);
        }
    }
    
    return entries;
}

/**
 * Find missing entries in Arabic file compared to English file
 * @param {string} englishContent - English language file content
 * @param {string} arabicContent - Arabic language file content
 * @returns {Map} - Map of missing entries (key -> English value)
 */
function findMissingEntries(englishContent, arabicContent) {
    const englishEntries = parseLanguageFile(englishContent);
    const arabicEntries = parseLanguageFile(arabicContent);
    const missingEntries = new Map();
    
    for (const [key, englishValue] of englishEntries) {
        if (!arabicEntries.has(key)) {
            missingEntries.set(key, englishValue);
        }
    }
    
    return missingEntries;
}

/**
 * Generate Arabic translations for missing entries
 * @param {Map} missingEntries - Missing entries map
 * @returns {Map} - Map of key -> Arabic translation
 */
function generateArabicTranslations(missingEntries) {
    const translations = new Map();
    
    for (const [key, englishValue] of missingEntries) {
        const arabicTranslation = getArabicTranslation(key, englishValue);
        // Add RTL markers for proper display
        const rtlTranslation = `‏${arabicTranslation}‏`;
        translations.set(key, rtlTranslation);
    }
    
    return translations;
}

/**
 * Format translations as language file entries
 * @param {Map} translations - Translations map
 * @returns {string} - Formatted language file entries
 */
function formatLanguageEntries(translations) {
    const entries = [];
    
    for (const [key, value] of translations) {
        entries.push(`${key}=${value}`);
    }
    
    return entries.join('\n');
}

/**
 * Get statistics about missing entries
 * @param {Map} englishEntries - English entries
 * @param {Map} arabicEntries - Arabic entries
 * @param {Map} missingEntries - Missing entries
 * @returns {Object} - Statistics object
 */
function getStatistics(englishEntries, arabicEntries, missingEntries) {
    return {
        englishTotal: englishEntries.size,
        arabicTotal: arabicEntries.size,
        missingCount: missingEntries.size,
        completionRate: ((arabicEntries.size / englishEntries.size) * 100).toFixed(1) + '%',
        missingRate: ((missingEntries.size / englishEntries.size) * 100).toFixed(1) + '%'
    };
}

/**
 * Categorize missing entries by type
 * @param {Map} missingEntries - Missing entries
 * @returns {Object} - Categorized entries
 */
function categorizeMissingEntries(missingEntries) {
    const categories = {
        accessibility: new Map(),
        achievements: new Map(),
        items: new Map(),
        blocks: new Map(),
        entities: new Map(),
        biomes: new Map(),
        enchantments: new Map(),
        effects: new Map(),
        ui: new Map(),
        commands: new Map(),
        other: new Map()
    };
    
    for (const [key, value] of missingEntries) {
        if (key.includes('accessibility')) {
            categories.accessibility.set(key, value);
        } else if (key.includes('achievement')) {
            categories.achievements.set(key, value);
        } else if (key.includes('item.')) {
            categories.items.set(key, value);
        } else if (key.includes('block.')) {
            categories.blocks.set(key, value);
        } else if (key.includes('entity.')) {
            categories.entities.set(key, value);
        } else if (key.includes('biome.')) {
            categories.biomes.set(key, value);
        } else if (key.includes('enchantment.')) {
            categories.enchantments.set(key, value);
        } else if (key.includes('effect.')) {
            categories.effects.set(key, value);
        } else if (key.includes('gui.') || key.includes('menu.') || key.includes('screen.')) {
            categories.ui.set(key, value);
        } else if (key.includes('command.') || key.includes('commands.')) {
            categories.commands.set(key, value);
        } else {
            categories.other.set(key, value);
        }
    }
    
    return categories;
}

/**
 * Process missing entries in batches to avoid memory issues
 * @param {Map} missingEntries - Missing entries
 * @param {number} batchSize - Size of each batch
 * @returns {Array} - Array of batches
 */
function processMissingEntriesInBatches(missingEntries, batchSize = 100) {
    const batches = [];
    const entries = Array.from(missingEntries.entries());
    
    for (let i = 0; i < entries.length; i += batchSize) {
        const batch = new Map(entries.slice(i, i + batchSize));
        batches.push(batch);
    }
    
    return batches;
}

/**
 * Generate a report of missing entries
 * @param {string} englishContent - English language file content
 * @param {string} arabicContent - Arabic language file content
 * @returns {Object} - Report object
 */
function generateMissingEntriesReport(englishContent, arabicContent) {
    const englishEntries = parseLanguageFile(englishContent);
    const arabicEntries = parseLanguageFile(arabicContent);
    const missingEntries = findMissingEntries(englishContent, arabicContent);
    const statistics = getStatistics(englishEntries, arabicEntries, missingEntries);
    const categories = categorizeMissingEntries(missingEntries);
    
    return {
        statistics,
        categories,
        missingEntries,
        englishEntries,
        arabicEntries
    };
}

export {
    parseLanguageFile,
    findMissingEntries,
    generateArabicTranslations,
    formatLanguageEntries,
    getStatistics,
    categorizeMissingEntries,
    processMissingEntriesInBatches,
    generateMissingEntriesReport
};
