/**
 * Quick Test for Arabic Language Fix
 * Simple test to verify basic functionality
 * @version 2.0.0
 */

import { processArabicText, needsArabicProcessing } from './textProcessor.js';
import { processChatText } from './rtlSupport.js';

/**
 * Run a quick test of Arabic processing
 */
function runQuickTest() {
    console.log("=== Quick Arabic Test ===");
    
    const testTexts = [
        "مرحبا",
        "السلام عليكم",
        "Hello World",
        "مرحبا Hello",
        "123 مرحبا"
    ];
    
    for (const text of testTexts) {
        const needsProcessing = needsArabicProcessing(text);
        console.log(`Text: "${text}" - Needs Processing: ${needsProcessing}`);
        
        if (needsProcessing) {
            try {
                const processed = processArabicText(text);
                const rtlProcessed = processChatText(text);
                console.log(`  Processed: "${processed}"`);
                console.log(`  RTL: "${rtlProcessed}"`);
            } catch (error) {
                console.error(`  Error: ${error.message}`);
            }
        }
    }
    
    console.log("=== Test Complete ===");
}

export { runQuickTest };
