/**
 * RTL (Right-to-Left) Support Module
 * Provides enhanced RTL text direction support for Arabic text in Minecraft UI
 * @version 2.0.0
 */

import { processArabicText, needsArabicProcessing } from './textProcessor.js';

// RTL Unicode control characters
const RTL_CONTROLS = {
    RLE: '\u202B',      // Right-to-Left Embedding
    LRE: '\u202A',      // Left-to-Right Embedding
    PDF: '\u202C',      // Pop Directional Formatting
    RLO: '\u202E',      // Right-to-Left Override
    LRO: '\u202D',      // Left-to-Right Override
    RLM: '\u200F',      // Right-to-Left Mark
    LRM: '\u200E',      // Left-to-Right Mark
    ALM: '\u061C'       // Arabic Letter Mark
};

// Bidirectional character types
const BIDI_TYPES = {
    ARABIC: /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/,
    LATIN: /[A-Za-z]/,
    DIGIT: /[0-9]/,
    ARABIC_DIGIT: /[٠-٩۰-۹]/,
    PUNCTUATION: /[!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~،؛؟]/,
    WHITESPACE: /\s/
};

/**
 * Detect text direction based on content
 * @param {string} text - Text to analyze
 * @returns {string} - 'rtl', 'ltr', or 'mixed'
 */
function detectTextDirection(text) {
    if (!text) return 'ltr';
    
    let arabicCount = 0;
    let latinCount = 0;
    
    for (const char of text) {
        if (BIDI_TYPES.ARABIC.test(char)) {
            arabicCount++;
        } else if (BIDI_TYPES.LATIN.test(char)) {
            latinCount++;
        }
    }
    
    if (arabicCount > latinCount) {
        return 'rtl';
    } else if (latinCount > arabicCount) {
        return 'ltr';
    } else {
        return 'mixed';
    }
}

/**
 * Apply RTL formatting to text
 * @param {string} text - Text to format
 * @param {boolean} forceRTL - Force RTL direction
 * @returns {string} - RTL formatted text
 */
function applyRTLFormatting(text, forceRTL = false) {
    if (!text) return text;
    
    const direction = detectTextDirection(text);
    
    if (direction === 'rtl' || forceRTL) {
        // Apply RTL override for strong RTL direction
        return `${RTL_CONTROLS.RLO}${text}${RTL_CONTROLS.PDF}`;
    } else if (direction === 'mixed') {
        // Apply RTL embedding for mixed content
        return `${RTL_CONTROLS.RLE}${text}${RTL_CONTROLS.PDF}`;
    }
    
    return text;
}

/**
 * Process text for RTL display with Arabic shaping
 * @param {string} text - Text to process
 * @param {Object} options - Processing options
 * @returns {string} - Processed RTL text
 */
function processRTLText(text, options = {}) {
    const {
        enableShaping = true,
        enableRTLMarks = true,
        forceDirection = null,
        preserveNumbers = true
    } = options;
    
    if (!needsArabicProcessing(text)) {
        return text;
    }
    
    let processedText = text;
    
    // Apply Arabic shaping if enabled
    if (enableShaping) {
        processedText = processArabicText(text, true, true);
    }
    
    // Handle mixed content (Arabic + Latin/Numbers)
    if (preserveNumbers) {
        processedText = preserveNumberDirection(processedText);
    }
    
    // Apply RTL formatting
    if (forceDirection === 'rtl') {
        processedText = applyRTLFormatting(processedText, true);
    } else if (forceDirection === 'ltr') {
        // Keep as LTR
        processedText = `${RTL_CONTROLS.LRO}${processedText}${RTL_CONTROLS.PDF}`;
    } else {
        // Auto-detect direction
        processedText = applyRTLFormatting(processedText);
    }
    
    // Add RTL marks if enabled
    if (enableRTLMarks) {
        processedText = addRTLMarks(processedText);
    }
    
    return processedText;
}

/**
 * Preserve number direction in mixed text
 * @param {string} text - Text with numbers
 * @returns {string} - Text with preserved number direction
 */
function preserveNumberDirection(text) {
    // Wrap Latin numbers with LTR marks to preserve their direction
    return text.replace(/(\d+)/g, `${RTL_CONTROLS.LRM}$1${RTL_CONTROLS.LRM}`);
}

/**
 * Add appropriate RTL marks to text
 * @param {string} text - Text to mark
 * @returns {string} - Text with RTL marks
 */
function addRTLMarks(text) {
    // Add Arabic Letter Mark at the beginning if text starts with Arabic
    if (BIDI_TYPES.ARABIC.test(text.charAt(0))) {
        text = RTL_CONTROLS.ALM + text;
    }
    
    // Add RLM after Arabic text before punctuation
    text = text.replace(/([\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF])([!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~])/g, 
                       `$1${RTL_CONTROLS.RLM}$2`);
    
    return text;
}

/**
 * Process text for UI elements (buttons, labels, etc.)
 * @param {string} text - UI text to process
 * @returns {string} - Processed UI text
 */
function processUIText(text) {
    return processRTLText(text, {
        enableShaping: true,
        enableRTLMarks: true,
        forceDirection: null,
        preserveNumbers: true
    });
}

/**
 * Process text for chat/messaging
 * @param {string} text - Chat text to process
 * @returns {string} - Processed chat text
 */
function processChatText(text) {
    return processRTLText(text, {
        enableShaping: true,
        enableRTLMarks: false, // Less aggressive for chat
        forceDirection: null,
        preserveNumbers: true
    });
}

/**
 * Process text for signs and displays
 * @param {string} text - Sign text to process
 * @returns {string} - Processed sign text
 */
function processSignText(text) {
    return processRTLText(text, {
        enableShaping: true,
        enableRTLMarks: true,
        forceDirection: 'rtl', // Force RTL for signs
        preserveNumbers: true
    });
}

/**
 * Clean RTL formatting from text
 * @param {string} text - Text with RTL formatting
 * @returns {string} - Clean text
 */
function cleanRTLFormatting(text) {
    if (!text) return text;
    
    // Remove all RTL control characters
    return text.replace(/[\u202A-\u202E\u200E\u200F\u061C]/g, '');
}

/**
 * Check if text contains RTL formatting
 * @param {string} text - Text to check
 * @returns {boolean} - True if contains RTL formatting
 */
function hasRTLFormatting(text) {
    return /[\u202A-\u202E\u200E\u200F\u061C]/.test(text);
}

/**
 * Get text direction information
 * @param {string} text - Text to analyze
 * @returns {Object} - Direction information
 */
function getTextDirectionInfo(text) {
    const direction = detectTextDirection(text);
    const hasRTL = hasRTLFormatting(text);
    const needsProcessing = needsArabicProcessing(text);
    
    return {
        direction,
        hasRTLFormatting: hasRTL,
        needsArabicProcessing: needsProcessing,
        isArabic: BIDI_TYPES.ARABIC.test(text),
        isLatin: BIDI_TYPES.LATIN.test(text),
        isMixed: direction === 'mixed'
    };
}

export {
    detectTextDirection,
    applyRTLFormatting,
    processRTLText,
    processUIText,
    processChatText,
    processSignText,
    cleanRTLFormatting,
    hasRTLFormatting,
    getTextDirectionInfo,
    RTL_CONTROLS,
    BIDI_TYPES
};
