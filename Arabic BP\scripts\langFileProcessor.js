/**
 * Language File Processor
 * Processes language files to add RTL markers for better menu display
 * @version 2.0.0
 */

import { needsArabicProcessing } from './textProcessor.js';

// RTL markers
const RLM = '\u200F'; // Right-to-Left Mark

/**
 * Process a language file line to add RTL markers
 * @param {string} line - Language file line
 * @returns {string} - Processed line
 */
function processLanguageLine(line) {
    if (!line || line.startsWith('#') || line.startsWith('//')) {
        return line; // Skip comments
    }
    
    const equalIndex = line.indexOf('=');
    if (equalIndex === -1) {
        return line; // Skip invalid lines
    }
    
    const key = line.substring(0, equalIndex);
    const value = line.substring(equalIndex + 1);
    
    if (needsArabicProcessing(value)) {
        // Add RTL markers around Arabic text
        const processedValue = `${RLM}${value}${RLM}`;
        return `${key}=${processedValue}`;
    }
    
    return line;
}

/**
 * Process language file content
 * @param {string} content - Language file content
 * @returns {string} - Processed content
 */
function processLanguageFile(content) {
    const lines = content.split('\n');
    const processedLines = lines.map(processLanguageLine);
    return processedLines.join('\n');
}

/**
 * Get language processing statistics
 * @param {string} content - Language file content
 * @returns {Object} - Processing statistics
 */
function getLanguageStats(content) {
    const lines = content.split('\n');
    let totalLines = 0;
    let arabicLines = 0;
    let processedLines = 0;
    
    for (const line of lines) {
        if (line && !line.startsWith('#') && !line.startsWith('//')) {
            totalLines++;
            
            const equalIndex = line.indexOf('=');
            if (equalIndex !== -1) {
                const value = line.substring(equalIndex + 1);
                if (needsArabicProcessing(value)) {
                    arabicLines++;
                    if (value.includes(RLM)) {
                        processedLines++;
                    }
                }
            }
        }
    }
    
    return {
        totalLines,
        arabicLines,
        processedLines,
        processingRate: arabicLines > 0 ? (processedLines / arabicLines * 100).toFixed(1) + '%' : '0%'
    };
}

export {
    processLanguageLine,
    processLanguageFile,
    getLanguageStats,
    RLM
};
