/**
 * Performance Optimization Module
 * Provides debouncing, throttling, and batch processing for Arabic text processing
 * @version 2.0.0
 */

import { system } from "@minecraft/server";

// Performance configuration
const PERFORMANCE_CONFIG = {
    DEBOUNCE_DELAY: 100,        // ms
    THROTTLE_DELAY: 50,         // ms
    BATCH_SIZE: 10,             // items per batch
    MAX_QUEUE_SIZE: 100,        // maximum queue size
    TICK_BUDGET: 5,             // max ms per tick
    CLEANUP_INTERVAL: 6000      // ticks (5 minutes)
};

// Debounce storage
const debounceTimers = new Map();
const throttleTimers = new Map();
const processingQueue = [];
let isProcessingQueue = false;

/**
 * Debounce function execution
 * @param {string} key - Unique key for the debounced function
 * @param {Function} func - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} - Debounced function
 */
function debounce(key, func, delay = PERFORMANCE_CONFIG.DEBOUNCE_DELAY) {
    return function(...args) {
        // Clear existing timer
        if (debounceTimers.has(key)) {
            system.clearRun(debounceTimers.get(key));
        }
        
        // Set new timer
        const timerId = system.runTimeout(() => {
            debounceTimers.delete(key);
            func.apply(this, args);
        }, Math.ceil(delay / 50)); // Convert ms to ticks (50ms per tick)
        
        debounceTimers.set(key, timerId);
    };
}

/**
 * Throttle function execution
 * @param {string} key - Unique key for the throttled function
 * @param {Function} func - Function to throttle
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} - Throttled function
 */
function throttle(key, func, delay = PERFORMANCE_CONFIG.THROTTLE_DELAY) {
    return function(...args) {
        if (!throttleTimers.has(key)) {
            func.apply(this, args);
            
            const timerId = system.runTimeout(() => {
                throttleTimers.delete(key);
            }, Math.ceil(delay / 50)); // Convert ms to ticks
            
            throttleTimers.set(key, timerId);
        }
    };
}

/**
 * Add task to processing queue
 * @param {Object} task - Task to process
 * @param {Function} task.processor - Processing function
 * @param {Array} task.args - Arguments for processor
 * @param {Function} task.callback - Callback function
 * @param {number} task.priority - Task priority (lower = higher priority)
 */
function queueTask(task) {
    if (processingQueue.length >= PERFORMANCE_CONFIG.MAX_QUEUE_SIZE) {
        console.warn("Processing queue is full, dropping task");
        return false;
    }
    
    // Insert task based on priority
    const insertIndex = processingQueue.findIndex(t => t.priority > task.priority);
    if (insertIndex === -1) {
        processingQueue.push(task);
    } else {
        processingQueue.splice(insertIndex, 0, task);
    }
    
    // Start processing if not already running
    if (!isProcessingQueue) {
        startQueueProcessing();
    }
    
    return true;
}

/**
 * Start processing the task queue
 */
function startQueueProcessing() {
    if (isProcessingQueue) return;
    
    isProcessingQueue = true;
    
    const processNextBatch = () => {
        const startTime = Date.now();
        let processed = 0;
        
        while (processingQueue.length > 0 && 
               processed < PERFORMANCE_CONFIG.BATCH_SIZE &&
               (Date.now() - startTime) < PERFORMANCE_CONFIG.TICK_BUDGET) {
            
            const task = processingQueue.shift();
            
            try {
                const result = task.processor(...task.args);
                if (task.callback) {
                    task.callback(null, result);
                }
            } catch (error) {
                console.error("Error processing queued task:", error);
                if (task.callback) {
                    task.callback(error, null);
                }
            }
            
            processed++;
        }
        
        // Continue processing if there are more tasks
        if (processingQueue.length > 0) {
            system.runTimeout(processNextBatch, 1); // Next tick
        } else {
            isProcessingQueue = false;
        }
    };
    
    system.runTimeout(processNextBatch, 1);
}

/**
 * Create a rate-limited version of a function
 * @param {Function} func - Function to rate limit
 * @param {number} maxCalls - Maximum calls per period
 * @param {number} period - Period in milliseconds
 * @returns {Function} - Rate limited function
 */
function rateLimit(func, maxCalls = 10, period = 1000) {
    const calls = [];
    
    return function(...args) {
        const now = Date.now();
        
        // Remove old calls outside the period
        while (calls.length > 0 && calls[0] <= now - period) {
            calls.shift();
        }
        
        // Check if we can make the call
        if (calls.length < maxCalls) {
            calls.push(now);
            return func.apply(this, args);
        } else {
            console.warn("Rate limit exceeded, call dropped");
            return null;
        }
    };
}

/**
 * Batch process items with automatic chunking
 * @param {Array} items - Items to process
 * @param {Function} processor - Processing function
 * @param {number} batchSize - Size of each batch
 * @returns {Promise} - Promise that resolves when all items are processed
 */
function batchProcess(items, processor, batchSize = PERFORMANCE_CONFIG.BATCH_SIZE) {
    return new Promise((resolve, reject) => {
        const results = [];
        let currentIndex = 0;
        
        const processNextBatch = () => {
            const batch = items.slice(currentIndex, currentIndex + batchSize);
            
            if (batch.length === 0) {
                resolve(results);
                return;
            }
            
            try {
                const batchResults = batch.map(processor);
                results.push(...batchResults);
                currentIndex += batchSize;
                
                // Schedule next batch
                system.runTimeout(processNextBatch, 1);
            } catch (error) {
                reject(error);
            }
        };
        
        processNextBatch();
    });
}

/**
 * Memory-efficient text processing with chunking
 * @param {string} text - Text to process
 * @param {Function} processor - Processing function
 * @param {number} chunkSize - Size of each chunk
 * @returns {string} - Processed text
 */
function processTextInChunks(text, processor, chunkSize = 500) {
    if (text.length <= chunkSize) {
        return processor(text);
    }
    
    let result = '';
    for (let i = 0; i < text.length; i += chunkSize) {
        const chunk = text.substring(i, i + chunkSize);
        result += processor(chunk);
    }
    
    return result;
}

/**
 * Clean up performance optimization resources
 */
function cleanup() {
    // Clear all debounce timers
    debounceTimers.forEach(timerId => system.clearRun(timerId));
    debounceTimers.clear();
    
    // Clear all throttle timers
    throttleTimers.forEach(timerId => system.clearRun(timerId));
    throttleTimers.clear();
    
    // Clear processing queue
    processingQueue.length = 0;
    isProcessingQueue = false;
}

/**
 * Get performance statistics
 * @returns {Object} - Performance statistics
 */
function getPerformanceStats() {
    return {
        debounceTimers: debounceTimers.size,
        throttleTimers: throttleTimers.size,
        queueSize: processingQueue.length,
        isProcessingQueue,
        config: PERFORMANCE_CONFIG
    };
}

// Automatic cleanup every 5 minutes
system.runInterval(() => {
    // Clean up expired timers and optimize memory
    if (debounceTimers.size > 50) {
        console.log("Cleaning up performance optimization resources");
        cleanup();
    }
}, PERFORMANCE_CONFIG.CLEANUP_INTERVAL);

export {
    debounce,
    throttle,
    queueTask,
    rateLimit,
    batchProcess,
    processTextInChunks,
    cleanup,
    getPerformanceStats,
    PERFORMANCE_CONFIG
};
