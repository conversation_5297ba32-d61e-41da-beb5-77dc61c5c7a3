/**
 * Add Missing Translations Script
 * Adds missing Arabic translations to the language file
 * @version 2.0.0
 */

import { 
    generateMissingEntriesReport, 
    generateArabicTranslations, 
    formatLanguageEntries 
} from './findMissingEntries.js';

// Priority translations for most important missing entries
const PRIORITY_TRANSLATIONS = {
    // Accessibility - High Priority
    "accessibility.disableTTS": "‏تم تعطيل تحويل النص إلى كلام‏",
    "accessibility.enableTTS": "‏تم تمكين تحويل النص إلى كلام‏",
    "accessibility.chat.tts.gamepad.back": "‏اضغط %s للرجوع‏",
    "accessibility.chat.tts.gamepad.exitChat": "‏اضغط %s للخروج من الدردشة‏",
    "accessibility.chat.tts.gamepad.sendMessage": "‏اضغط %s لإرسال الرسالة‏",
    "accessibility.chat.tts.says": "‏%s يقول %s‏",
    "accessibility.chat.tts.hideChat": "‏إخفاء الدردشة‏",
    "accessibility.chat.tts.keyboard": "‏لوحة المفاتيح‏",
    "accessibility.chat.tts.muteChatToggle": "‏كتم الجميع‏",
    "accessibility.chat.tts.sendChatMessage": "‏إرسال‏",
    "accessibility.chat.tts.textboxTitle": "‏دردشة‏",
    "accessibility.game.playerView": "‏عرض اللاعب‏",
    "accessibility.loading.done": "‏تم‏",
    "accessibility.saving.done": "‏تم‏",
    "accessibility.downloading.start": "‏بدأ التنزيل‏",
    "accessibility.downloading.Progress": "‏جاري التنزيل %s بالمائة‏",
    "accessibility.downloading.canceled": "‏تم إلغاء التنزيل‏",
    "accessibility.downloading.complete": "‏اكتمل التنزيل‏",
    "accessibility.importing.start": "‏بدأ الاستيراد‏",
    "accessibility.importing.Progress": "‏جاري الاستيراد %s بالمائة‏",
    "accessibility.importing.canceled": "‏تم إلغاء الاستيراد‏",
    "accessibility.importing.complete": "‏اكتمل الاستيراد‏",
    "accessibility.signin.xbl": "‏جاري تسجيل الدخول إلى Xbox Live‏",
    "accessibility.chat.howtoopen": "‏اضغط %s لفتح الدردشة‏",
    "accessibility.chat.howtoemote": "‏اضغط أو اضغط مطولاً %s للتعبير‏",
    "accessibility.key.mouseButton": "‏زر الفأرة %s‏",
    "accessibility.key.gamepad": "‏وحدة التحكم %s‏",
    "accessibility.list.or.two": "‏%s أو %s‏",
    "accessibility.list.or.three_or_more.first": "‏%s‏",
    "accessibility.list.or.three_or_more.inner": "‏، %s‏",
    "accessibility.list.or.three_or_more.last": "‏، أو %s‏",
    "accessibility.button.tts.title": "‏زر‏",
    "accessibility.checkbox.tts.title": "‏مربع اختيار‏",
    "accessibility.checkbox.tts.status": "‏الحالة %s‏",
    "accessibility.dropdown.tts.title": "‏قائمة منسدلة‏",
    "accessibility.image.tts.title": "‏صورة‏",
    "accessibility.screen.tts.title": "‏شاشة %s‏",
    "accessibility.slider.tts.title": "‏شريط تمرير‏",
    "accessibility.tab.tts.title": "‏تبويب‏",
    "accessibility.textbox.tts.title": "‏مربع نص‏",
    "accessibility.toggle.tts.title": "‏تبديل‏",
    "accessibility.scrollbar.tts.title": "‏شريط التمرير‏",
    "accessibility.button.navigateLeft": "‏سهم يسار‏",
    "accessibility.button.navigateRight": "‏سهم يمين‏",
    "accessibility.button.close": "‏إغلاق‏",
    "accessibility.button.back": "‏رجوع‏",
    "accessibility.button.exit": "‏خروج‏",
    "accessibility.button.filter": "‏تصفية‏",
    "accessibility.button.filter.appliedCount": "‏ %s مطبق‏",
    "accessibility.button.sort": "‏ترتيب‏",

    // Common UI Elements
    "gui.done": "‏تم‏",
    "gui.cancel": "‏إلغاء‏",
    "gui.back": "‏رجوع‏",
    "gui.ok": "‏موافق‏",
    "gui.yes": "‏نعم‏",
    "gui.no": "‏لا‏",
    "gui.save": "‏حفظ‏",
    "gui.load": "‏تحميل‏",
    "gui.delete": "‏حذف‏",
    "gui.edit": "‏تحرير‏",
    "gui.create": "‏إنشاء‏",
    "gui.join": "‏انضمام‏",
    "gui.leave": "‏مغادرة‏",
    "gui.start": "‏بدء‏",
    "gui.stop": "‏توقف‏",
    "gui.pause": "‏إيقاف مؤقت‏",
    "gui.resume": "‏استئناف‏",
    "gui.restart": "‏إعادة تشغيل‏",
    "gui.reset": "‏إعادة تعيين‏",
    "gui.apply": "‏تطبيق‏",
    "gui.close": "‏إغلاق‏",
    "gui.exit": "‏خروج‏",
    "gui.settings": "‏إعدادات‏",
    "gui.options": "‏خيارات‏",
    "gui.help": "‏مساعدة‏",
    "gui.about": "‏حول‏",

    // Menu Items
    "menu.singleplayer": "‏لاعب واحد‏",
    "menu.multiplayer": "‏متعدد اللاعبين‏",
    "menu.options": "‏خيارات‏",
    "menu.quit": "‏خروج من اللعبة‏",
    "menu.returnToMenu": "‏العودة إلى القائمة الرئيسية‏",
    "menu.returnToGame": "‏العودة إلى اللعبة‏",
    "menu.shareToLan": "‏مشاركة على الشبكة المحلية‏",
    "menu.generatingLevel": "‏جاري إنشاء العالم‏",
    "menu.loadingLevel": "‏جاري تحميل العالم‏",
    "menu.savingLevel": "‏جاري حفظ العالم‏",

    // Game Mode
    "gameMode.survival": "‏البقاء‏",
    "gameMode.creative": "‏الإبداع‏",
    "gameMode.adventure": "‏المغامرة‏",
    "gameMode.spectator": "‏المشاهدة‏",
    "gameMode.hardcore": "‏صعب جداً‏",

    // Difficulty
    "options.difficulty.peaceful": "‏سلمي‏",
    "options.difficulty.easy": "‏سهل‏",
    "options.difficulty.normal": "‏عادي‏",
    "options.difficulty.hard": "‏صعب‏",

    // Common Game Elements
    "item.minecraft.stone": "‏حجر‏",
    "item.minecraft.dirt": "‏تراب‏",
    "item.minecraft.grass_block": "‏كتلة عشب‏",
    "item.minecraft.cobblestone": "‏حجر مرصوف‏",
    "item.minecraft.oak_planks": "‏ألواح بلوط‏",
    "item.minecraft.oak_log": "‏جذع بلوط‏",
    "item.minecraft.iron_ingot": "‏سبيكة حديد‏",
    "item.minecraft.gold_ingot": "‏سبيكة ذهب‏",
    "item.minecraft.diamond": "‏ماس‏",
    "item.minecraft.coal": "‏فحم‏",
    "item.minecraft.water_bucket": "‏دلو ماء‏",
    "item.minecraft.lava_bucket": "‏دلو حمم‏",

    // Blocks
    "block.minecraft.stone": "‏حجر‏",
    "block.minecraft.dirt": "‏تراب‏",
    "block.minecraft.grass_block": "‏كتلة عشب‏",
    "block.minecraft.cobblestone": "‏حجر مرصوف‏",
    "block.minecraft.oak_planks": "‏ألواح بلوط‏",
    "block.minecraft.oak_log": "‏جذع بلوط‏",
    "block.minecraft.water": "‏ماء‏",
    "block.minecraft.lava": "‏حمم‏",
    "block.minecraft.sand": "‏رمل‏",
    "block.minecraft.gravel": "‏حصى‏",
    "block.minecraft.glass": "‏زجاج‏",
    "block.minecraft.obsidian": "‏سبج‏",
    "block.minecraft.bedrock": "‏صخر أساسي‏",

    // Entities
    "entity.minecraft.zombie": "‏زومبي‏",
    "entity.minecraft.skeleton": "‏هيكل عظمي‏",
    "entity.minecraft.creeper": "‏كريبر‏",
    "entity.minecraft.spider": "‏عنكبوت‏",
    "entity.minecraft.enderman": "‏إندرمان‏",
    "entity.minecraft.pig": "‏خنزير‏",
    "entity.minecraft.cow": "‏بقرة‏",
    "entity.minecraft.chicken": "‏دجاجة‏",
    "entity.minecraft.sheep": "‏خروف‏",
    "entity.minecraft.horse": "‏حصان‏",
    "entity.minecraft.wolf": "‏ذئب‏",
    "entity.minecraft.cat": "‏قطة‏",
    "entity.minecraft.villager": "‏قروي‏",

    // Status Effects
    "effect.minecraft.speed": "‏سرعة‏",
    "effect.minecraft.slowness": "‏بطء‏",
    "effect.minecraft.haste": "‏عجلة‏",
    "effect.minecraft.mining_fatigue": "‏إرهاق التعدين‏",
    "effect.minecraft.strength": "‏قوة‏",
    "effect.minecraft.instant_health": "‏صحة فورية‏",
    "effect.minecraft.instant_damage": "‏ضرر فوري‏",
    "effect.minecraft.jump_boost": "‏تعزيز القفز‏",
    "effect.minecraft.nausea": "‏غثيان‏",
    "effect.minecraft.regeneration": "‏تجديد‏",
    "effect.minecraft.resistance": "‏مقاومة‏",
    "effect.minecraft.fire_resistance": "‏مقاومة النار‏",
    "effect.minecraft.water_breathing": "‏تنفس تحت الماء‏",
    "effect.minecraft.invisibility": "‏اختفاء‏",
    "effect.minecraft.blindness": "‏عمى‏",
    "effect.minecraft.night_vision": "‏رؤية ليلية‏",
    "effect.minecraft.hunger": "‏جوع‏",
    "effect.minecraft.weakness": "‏ضعف‏",
    "effect.minecraft.poison": "‏سم‏",
    "effect.minecraft.wither": "‏ذبول‏",

    // Enchantments
    "enchantment.minecraft.sharpness": "‏حدة‏",
    "enchantment.minecraft.protection": "‏حماية‏",
    "enchantment.minecraft.efficiency": "‏كفاءة‏",
    "enchantment.minecraft.unbreaking": "‏عدم الكسر‏",
    "enchantment.minecraft.fortune": "‏حظ‏",
    "enchantment.minecraft.silk_touch": "‏لمسة حريرية‏",
    "enchantment.minecraft.fire_aspect": "‏جانب النار‏",
    "enchantment.minecraft.knockback": "‏دفع للخلف‏",
    "enchantment.minecraft.looting": "‏نهب‏",
    "enchantment.minecraft.respiration": "‏تنفس‏",
    "enchantment.minecraft.aqua_affinity": "‏تقارب مائي‏",
    "enchantment.minecraft.thorns": "‏أشواك‏",
    "enchantment.minecraft.depth_strider": "‏خطوات العمق‏",
    "enchantment.minecraft.frost_walker": "‏مشي الصقيع‏",
    "enchantment.minecraft.mending": "‏إصلاح‏",
    "enchantment.minecraft.curse_of_binding": "‏لعنة الربط‏",
    "enchantment.minecraft.curse_of_vanishing": "‏لعنة الاختفاء‏",

    // Biomes
    "biome.minecraft.desert": "‏صحراء‏",
    "biome.minecraft.forest": "‏غابة‏",
    "biome.minecraft.ocean": "‏محيط‏",
    "biome.minecraft.plains": "‏سهول‏",
    "biome.minecraft.mountains": "‏جبال‏",
    "biome.minecraft.swamp": "‏مستنقع‏",
    "biome.minecraft.jungle": "‏أدغال‏",
    "biome.minecraft.taiga": "‏تايغا‏",
    "biome.minecraft.tundra": "‏تندرا‏",

    // Commands
    "commands.give.success": "‏تم إعطاء %s × %d لـ %s‏",
    "commands.teleport.success": "‏تم نقل %s إلى %s‏",
    "commands.gamemode.success.self": "‏تم تغيير وضع اللعبة إلى %s‏",
    "commands.gamemode.success.other": "‏تم تغيير وضع اللعبة لـ %s إلى %s‏",
    "commands.time.set": "‏تم تعيين الوقت إلى %s‏",
    "commands.weather.set.clear": "‏تم تغيير الطقس إلى صافي‏",
    "commands.weather.set.rain": "‏تم تغيير الطقس إلى ممطر‏",
    "commands.weather.set.thunder": "‏تم تغيير الطقس إلى عاصف‏"
};

/**
 * Get the most important missing translations to add first
 * @param {Map} missingEntries - All missing entries
 * @returns {Map} - Priority entries to add
 */
function getPriorityTranslations(missingEntries) {
    const priorityEntries = new Map();
    
    for (const [key, englishValue] of missingEntries) {
        if (PRIORITY_TRANSLATIONS[key]) {
            priorityEntries.set(key, PRIORITY_TRANSLATIONS[key]);
        }
    }
    
    return priorityEntries;
}

/**
 * Generate additional translations for remaining entries
 * @param {Map} remainingEntries - Entries not in priority list
 * @returns {Map} - Generated translations
 */
function generateAdditionalTranslations(remainingEntries) {
    return generateArabicTranslations(remainingEntries);
}

export {
    PRIORITY_TRANSLATIONS,
    getPriorityTranslations,
    generateAdditionalTranslations
};
