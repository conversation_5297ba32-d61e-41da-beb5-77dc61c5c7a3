/**
 * Plugin Text Handler
 * Handles Arabic text processing for text sent by other plugins
 * @version 2.0.0
 */

import { world, system } from "@minecraft/server";
import { processArabicText, needsArabicProcessing } from './textProcessor.js';
import { processChatText } from './rtlSupport.js';
import { throttle, debounce } from './performanceOptimizer.js';

// Configuration for plugin text handling
const PLUGIN_CONFIG = {
    ENABLE_PLUGIN_PROCESSING: true,     // Enable plugin text processing
    MONITOR_INTERVAL: 5,                // How often to check for plugin text (ticks)
    PROCESSED_MARKER: "؀",              // Marker for already processed text
    MAX_HISTORY_SIZE: 100,              // Maximum chat history to monitor
    ENABLE_TITLE_PROCESSING: true,      // Process title/subtitle text
    ENABLE_ACTIONBAR_PROCESSING: true,  // Process action bar text
    ENABLE_SCOREBOARD_PROCESSING: true, // Process scoreboard text
    DEBUG_MODE: false                   // Debug logging
};

// Storage for monitoring plugin text
let lastChatMessages = [];
let lastTitleText = "";
let lastActionBarText = "";
let processedTexts = new Set();

/**
 * Monitor chat messages for plugin-sent text
 */
const monitorChatMessages = throttle("chatMonitor", () => {
    if (!PLUGIN_CONFIG.ENABLE_PLUGIN_PROCESSING) return;
    
    try {
        // This is a workaround since we can't directly intercept plugin messages
        // We monitor for patterns that indicate plugin-sent messages
        
        // Note: This is limited by the current Bedrock Script API
        // We can only process text that goes through our event handlers
        
        if (PLUGIN_CONFIG.DEBUG_MODE) {
            console.log("Monitoring chat for plugin messages...");
        }
        
    } catch (error) {
        console.error("Error monitoring chat messages:", error);
    }
}, 100);

/**
 * Process title and subtitle text
 * @param {Player} player - Player to process title for
 * @param {string} title - Title text
 * @param {string} subtitle - Subtitle text
 */
function processTitleText(player, title, subtitle) {
    if (!PLUGIN_CONFIG.ENABLE_TITLE_PROCESSING) return;
    
    try {
        let processedTitle = title;
        let processedSubtitle = subtitle;
        
        if (title && needsArabicProcessing(title) && !title.startsWith(PLUGIN_CONFIG.PROCESSED_MARKER)) {
            processedTitle = PLUGIN_CONFIG.PROCESSED_MARKER + processChatText(title);
        }
        
        if (subtitle && needsArabicProcessing(subtitle) && !subtitle.startsWith(PLUGIN_CONFIG.PROCESSED_MARKER)) {
            processedSubtitle = PLUGIN_CONFIG.PROCESSED_MARKER + processChatText(subtitle);
        }
        
        // Note: Current Bedrock Script API doesn't allow setting title/subtitle
        // This is a limitation we need to work around
        
        if (PLUGIN_CONFIG.DEBUG_MODE) {
            console.log(`Title processing: "${title}" -> "${processedTitle}"`);
            console.log(`Subtitle processing: "${subtitle}" -> "${processedSubtitle}"`);
        }
        
    } catch (error) {
        console.error("Error processing title text:", error);
    }
}

/**
 * Process action bar text
 * @param {Player} player - Player to process action bar for
 * @param {string} text - Action bar text
 */
function processActionBarText(player, text) {
    if (!PLUGIN_CONFIG.ENABLE_ACTIONBAR_PROCESSING) return;
    
    try {
        if (text && needsArabicProcessing(text) && !text.startsWith(PLUGIN_CONFIG.PROCESSED_MARKER)) {
            const processedText = PLUGIN_CONFIG.PROCESSED_MARKER + processChatText(text);
            
            // Note: Current Bedrock Script API doesn't allow setting action bar text
            // This is a limitation we need to work around
            
            if (PLUGIN_CONFIG.DEBUG_MODE) {
                console.log(`Action bar processing: "${text}" -> "${processedText}"`);
            }
        }
        
    } catch (error) {
        console.error("Error processing action bar text:", error);
    }
}

/**
 * Process scoreboard text
 * @param {string} objectiveName - Scoreboard objective name
 * @param {string} displayName - Display name
 */
function processScoreboardText(objectiveName, displayName) {
    if (!PLUGIN_CONFIG.ENABLE_SCOREBOARD_PROCESSING) return;
    
    try {
        if (displayName && needsArabicProcessing(displayName) && !displayName.startsWith(PLUGIN_CONFIG.PROCESSED_MARKER)) {
            const processedDisplayName = PLUGIN_CONFIG.PROCESSED_MARKER + processChatText(displayName);
            
            // Note: Current Bedrock Script API has limited scoreboard modification capabilities
            // This is a limitation we need to work around
            
            if (PLUGIN_CONFIG.DEBUG_MODE) {
                console.log(`Scoreboard processing: "${displayName}" -> "${processedDisplayName}"`);
            }
        }
        
    } catch (error) {
        console.error("Error processing scoreboard text:", error);
    }
}

/**
 * Enhanced chat message interceptor for plugin messages
 */
function setupPluginChatInterceptor() {
    // Enhanced chat monitoring that can catch more plugin messages
    world.afterEvents.chatSend.subscribe((event) => {
        const { message, sender } = event;
        
        // Check if this might be a plugin message
        if (isLikelyPluginMessage(message, sender)) {
            if (needsArabicProcessing(message)) {
                // Process the message and try to replace it
                const processedMessage = processChatText(message);
                
                // Send the processed message
                // Note: We can't modify the original message after it's sent
                // This is a limitation of the current API
                
                if (PLUGIN_CONFIG.DEBUG_MODE) {
                    console.log(`Plugin message detected: "${message}" -> "${processedMessage}"`);
                }
            }
        }
    });
}

/**
 * Check if a message is likely from a plugin
 * @param {string} message - Message text
 * @param {Player} sender - Message sender
 * @returns {boolean} - True if likely a plugin message
 */
function isLikelyPluginMessage(message, sender) {
    // Heuristics to detect plugin messages
    const pluginIndicators = [
        message.includes('['),          // Common plugin prefix format
        message.includes(']'),          // Common plugin suffix format
        message.includes('§'),          // Color codes (common in plugins)
        message.includes('&'),          // Alternative color codes
        message.length > 100,           // Very long messages often from plugins
        /^\[.*\]/.test(message),       // Starts with [tag]
        /^<.*>/.test(message),         // Starts with <tag>
        message.includes('»'),          // Special characters often used by plugins
        message.includes('«'),
        message.includes('→'),
        message.includes('←'),
        message.includes('▶'),
        message.includes('◀')
    ];
    
    return pluginIndicators.some(indicator => indicator);
}

/**
 * Alternative approach: Monitor world messages
 */
function setupWorldMessageMonitor() {
    // Monitor world.sendMessage calls (this catches some plugin messages)
    const originalSendMessage = world.sendMessage;
    
    world.sendMessage = function(message) {
        if (typeof message === 'string' && needsArabicProcessing(message)) {
            if (!message.startsWith(PLUGIN_CONFIG.PROCESSED_MARKER)) {
                const processedMessage = PLUGIN_CONFIG.PROCESSED_MARKER + processChatText(message);
                return originalSendMessage.call(this, processedMessage);
            }
        }
        return originalSendMessage.call(this, message);
    };
}

/**
 * Setup player message monitoring
 */
function setupPlayerMessageMonitor() {
    // Monitor player.sendMessage calls
    const players = world.getPlayers();
    
    for (const player of players) {
        if (player.sendMessage && typeof player.sendMessage === 'function') {
            const originalSendMessage = player.sendMessage;
            
            player.sendMessage = function(message) {
                if (typeof message === 'string' && needsArabicProcessing(message)) {
                    if (!message.startsWith(PLUGIN_CONFIG.PROCESSED_MARKER)) {
                        const processedMessage = PLUGIN_CONFIG.PROCESSED_MARKER + processChatText(message);
                        return originalSendMessage.call(this, processedMessage);
                    }
                }
                return originalSendMessage.call(this, message);
            };
        }
    }
}

/**
 * Setup comprehensive plugin text monitoring
 */
function setupPluginTextMonitoring() {
    if (!PLUGIN_CONFIG.ENABLE_PLUGIN_PROCESSING) return;
    
    console.log("Setting up plugin text monitoring...");
    
    // Setup various monitoring approaches
    setupPluginChatInterceptor();
    setupWorldMessageMonitor();
    
    // Monitor for new players and setup their message monitoring
    world.afterEvents.playerSpawn.subscribe((event) => {
        system.runTimeout(() => {
            setupPlayerMessageMonitor();
        }, 20); // Wait a second for player to be fully loaded
    });
    
    // Initial setup for existing players
    setupPlayerMessageMonitor();
    
    // Regular monitoring interval
    system.runInterval(() => {
        monitorChatMessages();
        setupPlayerMessageMonitor(); // Re-setup in case of changes
    }, PLUGIN_CONFIG.MONITOR_INTERVAL);
    
    console.log("Plugin text monitoring setup complete");
}

/**
 * Get plugin text processing statistics
 * @returns {Object} - Statistics object
 */
function getPluginTextStats() {
    return {
        processedTextsCount: processedTexts.size,
        chatHistorySize: lastChatMessages.length,
        config: PLUGIN_CONFIG
    };
}

/**
 * Clear plugin text processing cache
 */
function clearPluginTextCache() {
    processedTexts.clear();
    lastChatMessages = [];
    lastTitleText = "";
    lastActionBarText = "";
}

export {
    setupPluginTextMonitoring,
    processTitleText,
    processActionBarText,
    processScoreboardText,
    getPluginTextStats,
    clearPluginTextCache,
    PLUGIN_CONFIG
};
