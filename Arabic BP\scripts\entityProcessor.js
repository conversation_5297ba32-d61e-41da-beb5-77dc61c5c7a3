/**
 * Entity and Game Element Processor
 * Handles Arabic text processing for entities, item frames, and other game elements
 * @version 2.0.0
 */

import { world, system } from "@minecraft/server";
import { processArabicText, needsArabicProcessing } from './textProcessor.js';
import { processSignText, processUIText } from './rtlSupport.js';
import { throttle, debounce } from './performanceOptimizer.js';

// Configuration for entity processing
const ENTITY_CONFIG = {
    MAX_DISTANCE: 8,                    // blocks
    PROCESSED_MARKER: "؀",              // marker for processed text
    ENTITY_TYPES: {
        ITEM_FRAME: "minecraft:item_frame",
        GLOW_ITEM_FRAME: "minecraft:glow_item_frame",
        ARMOR_STAND: "minecraft:armor_stand",
        VILLAGER: "minecraft:villager",
        ZOMBIE_VILLAGER: "minecraft:zombie_villager",
        HORSE: "minecraft:horse",
        DONKEY: "minecraft:donkey",
        MULE: "minecraft:mule",
        LLAMA: "minecraft:llama",
        CAT: "minecraft:cat",
        DOG: "minecraft:wolf",
        PARROT: "minecraft:parrot"
    },
    ENABLE_ITEM_FRAMES: true,
    ENABLE_ENTITY_NAMES: true,
    ENABLE_CUSTOM_NAMES: true,
    DEBUG_MODE: false
};

// Cache for processed entities to avoid reprocessing
const processedEntities = new Map();
const CACHE_CLEANUP_INTERVAL = 12000; // 10 minutes

/**
 * Process item frame text
 * @param {Entity} itemFrame - Item frame entity
 * @returns {boolean} - True if processed
 */
function processItemFrame(itemFrame) {
    try {
        const itemComponent = itemFrame.getComponent("minecraft:item");
        if (!itemComponent?.itemStack?.nameTag) return false;
        
        const nameTag = itemComponent.itemStack.nameTag;
        
        // Skip if already processed
        if (nameTag.startsWith(ENTITY_CONFIG.PROCESSED_MARKER)) return false;
        
        if (needsArabicProcessing(nameTag)) {
            const processedName = ENTITY_CONFIG.PROCESSED_MARKER + processUIText(nameTag);
            itemComponent.itemStack.nameTag = processedName;
            
            if (ENTITY_CONFIG.DEBUG_MODE) {
                console.log(`Processed item frame text: ${nameTag} -> ${processedName}`);
            }
            
            return true;
        }
        
        return false;
    } catch (error) {
        console.error("Error processing item frame:", error);
        return false;
    }
}

/**
 * Process entity name tag
 * @param {Entity} entity - Entity to process
 * @returns {boolean} - True if processed
 */
function processEntityNameTag(entity) {
    try {
        if (!entity.nameTag) return false;
        
        // Skip if already processed
        if (entity.nameTag.startsWith(ENTITY_CONFIG.PROCESSED_MARKER)) return false;
        
        if (needsArabicProcessing(entity.nameTag)) {
            const processedName = ENTITY_CONFIG.PROCESSED_MARKER + processUIText(entity.nameTag);
            entity.nameTag = processedName;
            
            if (ENTITY_CONFIG.DEBUG_MODE) {
                console.log(`Processed entity name: ${entity.nameTag} -> ${processedName}`);
            }
            
            return true;
        }
        
        return false;
    } catch (error) {
        console.error("Error processing entity name:", error);
        return false;
    }
}

/**
 * Process book content (if entity is holding a book)
 * @param {Entity} entity - Entity to check
 * @returns {boolean} - True if processed
 */
function processEntityBook(entity) {
    try {
        const inventory = entity.getComponent("minecraft:inventory");
        if (!inventory?.container) return false;
        
        let processed = false;
        
        for (let i = 0; i < inventory.container.size; i++) {
            const item = inventory.container.getItem(i);
            
            if (item?.typeId === "minecraft:written_book" || item?.typeId === "minecraft:writable_book") {
                // Process book title
                if (item.nameTag && !item.nameTag.startsWith(ENTITY_CONFIG.PROCESSED_MARKER)) {
                    if (needsArabicProcessing(item.nameTag)) {
                        item.nameTag = ENTITY_CONFIG.PROCESSED_MARKER + processUIText(item.nameTag);
                        inventory.container.setItem(i, item);
                        processed = true;
                    }
                }
                
                // Process book pages (if accessible)
                const bookComponent = item.getComponent("minecraft:book");
                if (bookComponent?.pages) {
                    for (let pageIndex = 0; pageIndex < bookComponent.pages.length; pageIndex++) {
                        const page = bookComponent.pages[pageIndex];
                        if (page.text && needsArabicProcessing(page.text)) {
                            page.text = processUIText(page.text);
                            processed = true;
                        }
                    }
                }
            }
        }
        
        return processed;
    } catch (error) {
        console.error("Error processing entity book:", error);
        return false;
    }
}

/**
 * Process all entities near a player
 * @param {Player} player - Player to check around
 * @returns {number} - Number of entities processed
 */
function processNearbyEntities(player) {
    try {
        const entities = player.dimension.getEntities({
            location: player.location,
            maxDistance: ENTITY_CONFIG.MAX_DISTANCE,
            excludeTypes: ["minecraft:player"]
        });
        
        let processedCount = 0;
        
        for (const entity of entities) {
            const entityId = entity.id;
            
            // Skip if recently processed
            if (processedEntities.has(entityId)) {
                const lastProcessed = processedEntities.get(entityId);
                if (Date.now() - lastProcessed < 5000) { // 5 seconds cooldown
                    continue;
                }
            }
            
            let entityProcessed = false;
            
            // Process item frames
            if (ENTITY_CONFIG.ENABLE_ITEM_FRAMES && 
                (entity.typeId === ENTITY_CONFIG.ENTITY_TYPES.ITEM_FRAME || 
                 entity.typeId === ENTITY_CONFIG.ENTITY_TYPES.GLOW_ITEM_FRAME)) {
                if (processItemFrame(entity)) {
                    entityProcessed = true;
                }
            }
            
            // Process entity names
            if (ENTITY_CONFIG.ENABLE_ENTITY_NAMES) {
                if (processEntityNameTag(entity)) {
                    entityProcessed = true;
                }
            }
            
            // Process entity books
            if (ENTITY_CONFIG.ENABLE_CUSTOM_NAMES) {
                if (processEntityBook(entity)) {
                    entityProcessed = true;
                }
            }
            
            if (entityProcessed) {
                processedEntities.set(entityId, Date.now());
                processedCount++;
            }
        }
        
        return processedCount;
    } catch (error) {
        console.error("Error processing nearby entities:", error);
        return 0;
    }
}

/**
 * Process lectern book content
 * @param {Block} lecternBlock - Lectern block
 * @returns {boolean} - True if processed
 */
function processLecternBook(lecternBlock) {
    try {
        const lecternComponent = lecternBlock.getComponent("minecraft:lectern");
        if (!lecternComponent?.book) return false;
        
        const book = lecternComponent.book;
        let processed = false;
        
        // Process book title
        if (book.nameTag && !book.nameTag.startsWith(ENTITY_CONFIG.PROCESSED_MARKER)) {
            if (needsArabicProcessing(book.nameTag)) {
                book.nameTag = ENTITY_CONFIG.PROCESSED_MARKER + processUIText(book.nameTag);
                processed = true;
            }
        }
        
        // Process book pages
        const bookComponent = book.getComponent("minecraft:book");
        if (bookComponent?.pages) {
            for (let pageIndex = 0; pageIndex < bookComponent.pages.length; pageIndex++) {
                const page = bookComponent.pages[pageIndex];
                if (page.text && needsArabicProcessing(page.text)) {
                    page.text = processUIText(page.text);
                    processed = true;
                }
            }
        }
        
        if (processed) {
            lecternComponent.book = book;
        }
        
        return processed;
    } catch (error) {
        console.error("Error processing lectern book:", error);
        return false;
    }
}

/**
 * Clean up entity processing cache
 */
function cleanupEntityCache() {
    const now = Date.now();
    const expireTime = 300000; // 5 minutes
    
    for (const [entityId, timestamp] of processedEntities.entries()) {
        if (now - timestamp > expireTime) {
            processedEntities.delete(entityId);
        }
    }
    
    if (ENTITY_CONFIG.DEBUG_MODE) {
        console.log(`Entity cache cleanup: ${processedEntities.size} entities remaining`);
    }
}

/**
 * Get entity processing statistics
 * @returns {Object} - Processing statistics
 */
function getEntityStats() {
    return {
        cachedEntities: processedEntities.size,
        config: ENTITY_CONFIG
    };
}

// Throttled entity processing function
const throttledEntityProcessing = throttle("entityProcessing", (player) => {
    return processNearbyEntities(player);
}, 200);

// Debounced lectern processing function
const debouncedLecternProcessing = debounce("lecternProcessing", (block) => {
    return processLecternBook(block);
}, 150);

// Automatic cache cleanup
system.runInterval(() => {
    cleanupEntityCache();
}, CACHE_CLEANUP_INTERVAL);

export {
    processNearbyEntities,
    processItemFrame,
    processEntityNameTag,
    processLecternBook,
    throttledEntityProcessing,
    debouncedLecternProcessing,
    cleanupEntityCache,
    getEntityStats,
    ENTITY_CONFIG
};
