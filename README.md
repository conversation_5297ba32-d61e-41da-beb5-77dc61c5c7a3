# Arabic Language Fix Enhanced for Minecraft Bedrock 1.21.100+

A comprehensive Arabic language support addon for Minecraft Bedrock Edition with enhanced performance, RTL support, and extensive character coverage.

## 🌟 Features

### ✨ Enhanced Arabic Support
- **Comprehensive Character Set**: Support for Arabic, Persian, Urdu, Pashto, Kurdish, Sindhi, Uyghur, and more
- **Contextual Forms**: Proper isolated, initial, medial, and final character forms
- **Ligature Processing**: Advanced Lam-Alef and other Arabic ligatures
- **Diacritic Handling**: Intelligent diacritic removal and processing

### 🚀 Performance Optimizations
- **Intelligent Caching**: LRU cache system for processed text
- **Debouncing & Throttling**: Prevents server lag with smart rate limiting
- **Batch Processing**: Efficient handling of multiple texts
- **Memory Management**: Automatic cleanup and optimization

### 🔄 RTL (Right-to-Left) Support
- **Bidirectional Text**: Proper handling of mixed Arabic-Latin text
- **UI Integration**: Enhanced RTL support for menus and interfaces
- **Direction Detection**: Automatic text direction detection
- **Unicode Controls**: Proper RTL formatting with Unicode control characters

### 🎮 Game Element Support
- **Chat Messages**: Real-time Arabic text processing in chat
- **Signs**: Both regular and hanging signs with RTL support
- **Item Names**: Custom item name processing
- **Entity Names**: Support for named entities (animals, villagers, etc.)
- **Item Frames**: Text processing for item frame contents
- **Books**: Support for written books and lecterns

## 📦 Installation

1. Download the addon files
2. Import both the Behavior Pack (`Arabic BP`) and Resource Pack (`Arabic RP`) into Minecraft
3. Enable both packs in your world settings
4. Ensure the Behavior Pack is above any other behavior packs that might conflict

## 🛠️ Configuration

Edit the `CONFIG` object in `scripts/index.js` to customize behavior:

```javascript
const CONFIG = {
    SIGN_CHECK_INTERVAL: 20,        // How often to check signs (ticks)
    ITEM_CHECK_INTERVAL: 30,        // How often to check items (ticks)
    ENTITY_CHECK_INTERVAL: 40,      // How often to check entities (ticks)
    MAX_DISTANCE: 6,                // Maximum distance for processing (blocks)
    PROCESSED_MARKER: "؀",          // Marker for processed text
    ENABLE_RTL_UI: true,            // Enable RTL support for UI
    ENABLE_ENTITY_NAMES: true,      // Enable entity name processing
    ENABLE_ITEM_FRAMES: true,       // Enable item frame processing
    DEBUG_MODE: false               // Enable debug logging
};
```

## 🧪 Testing

The addon includes a comprehensive test suite. Use these chat commands:

- `!arabic-test run` - Run all tests
- `!arabic-test validate <text>` - Validate specific Arabic text

## 📋 Supported Languages

- **Arabic** (العربية) - Full support with all regional variants
- **Persian/Farsi** (فارسی) - Complete character set including Persian-specific letters
- **Urdu** (اردو) - Full Urdu alphabet with proper contextual forms
- **Pashto** (پښتو) - Complete Pashto character support
- **Kurdish** (کوردی) - Sorani and Kurmanji variants
- **Sindhi** (سنڌي) - Full Sindhi alphabet
- **Uyghur** (ئۇيغۇرچە) - Complete Uyghur character set
- **Kazakh** (قازاقشا) - Arabic script Kazakh
- **Kyrgyz** (قىرعىزچا) - Arabic script Kyrgyz

## 🔧 Technical Details

### Architecture
- **Modular Design**: Separate modules for processing, RTL support, entities, and performance
- **Event-Driven**: Efficient event handling for chat, world load, and intervals
- **Error Handling**: Comprehensive error handling with fallbacks

### Performance Features
- **Text Caching**: Processed text is cached to avoid reprocessing
- **Smart Intervals**: Different intervals for different types of processing
- **Memory Optimization**: Automatic cleanup of old cache entries
- **Batch Processing**: Multiple texts processed efficiently in batches

### RTL Implementation
- **Unicode Standards**: Follows Unicode Bidirectional Algorithm
- **Control Characters**: Proper use of RTL/LTR embedding and override characters
- **Mixed Content**: Intelligent handling of Arabic-Latin mixed text
- **Number Preservation**: Maintains proper number direction in mixed text

## 🐛 Troubleshooting

### Common Issues

1. **Text not processing**: Check if Arabic characters are being detected
2. **Performance lag**: Reduce check intervals in configuration
3. **RTL not working**: Ensure `ENABLE_RTL_UI` is set to `true`
4. **Entity names not updating**: Check `ENABLE_ENTITY_NAMES` setting

### Debug Mode
Enable debug mode in configuration to see detailed processing logs:
```javascript
DEBUG_MODE: true
```

## 📊 Performance Monitoring

The addon includes built-in performance monitoring:
- Processing statistics are logged every 5 minutes
- Cache hit rates and processing counts are tracked
- Memory usage is monitored and optimized

## 🔄 Version History

### Version 2.0.0 (Current)
- Complete rewrite for Minecraft Bedrock 1.21.100+
- Enhanced performance with caching and optimization
- Comprehensive RTL support
- Extended language support
- Modular architecture
- Built-in testing suite

### Version 1.6.11 (Legacy)
- Basic Arabic text processing
- Simple sign and item name support
- Limited character set

## 🤝 Contributing

To contribute to this project:
1. Test the addon thoroughly
2. Report any issues with specific text examples
3. Suggest improvements for performance or compatibility
4. Submit language-specific character additions

## 📄 License

This project is open source and available for modification and redistribution.

## 🙏 Acknowledgments

- Unicode Consortium for bidirectional text standards
- Arabic typography experts for character form guidance
- Minecraft Bedrock community for testing and feedback
- Contributors to Arabic script digitization efforts

---

**Note**: This addon requires Minecraft Bedrock Edition 1.21.100 or later with experimental features enabled for Script API support.
